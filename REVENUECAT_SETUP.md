# RevenueCat Integration Guide for Jackpot Party Casino

## Prerequisites

1. Create a RevenueCat account at [revenuecat.com](https://revenuecat.com)
2. Set up your app in the RevenueCat dashboard
3. Configure your app store products (iOS App Store Connect / Google Play Console)

## Installation

The `react-native-purchases` package is already included in the project dependencies. For a real implementation, you would need to:

```bash
npm install react-native-purchases
```

## Configuration

### 1. RevenueCat Dashboard Setup

1. **Create App**: Add your app to RevenueCat dashboard
2. **Configure Products**: Set up your in-app purchase products
3. **Create Offerings**: Group products into offerings for easier management
4. **Get API Keys**: Copy your public SDK key

### 2. Environment Variables

Update your `.env` file:

```env
EXPO_PUBLIC_REVENUECAT_API_KEY=your_revenuecat_public_key_here
```

### 3. App Store Products

Create these products in your app store console:

#### iOS App Store Connect
- `com.jackpotparty.coins.small` - Starter Pack ($0.99)
- `com.jackpotparty.coins.medium` - Value Pack ($4.99)
- `com.jackpotparty.coins.large` - Mega Pack ($9.99)
- `com.jackpotparty.coins.premium` - Premium Pack ($19.99)

#### Google Play Console
- Same product IDs as iOS
- Configure pricing in all relevant markets

## Implementation

### 1. Replace Mock Implementation

The current `usePurchases` hook uses a mock implementation. To integrate with real RevenueCat:

```typescript
// src/hooks/usePurchases.ts
import Purchases, { 
  PurchasesOffering, 
  PurchasesPackage,
  CustomerInfo 
} from 'react-native-purchases';

// Replace mock initialization with:
const initializePurchases = useCallback(async () => {
  try {
    await Purchases.configure({
      apiKey: Constants.expoConfig?.extra?.revenuecatApiKey,
      appUserID: user?.id,
    });
    
    const offerings = await Purchases.getOfferings();
    const packages = offerings.current?.availablePackages || [];
    
    setState(prev => ({
      ...prev,
      offerings: packages,
      loading: false,
    }));
  } catch (error) {
    // Handle error
  }
}, [user]);

// Replace mock purchase with:
const purchase = useCallback(async (packageIdentifier: string) => {
  try {
    const packageToPurchase = state.offerings.find(
      pkg => pkg.identifier === packageIdentifier
    );
    
    if (!packageToPurchase) {
      throw new Error('Package not found');
    }
    
    const { customerInfo, productIdentifier } = await Purchases.purchasePackage(
      packageToPurchase
    );
    
    // Process successful purchase
    // Update user balance in Supabase
    // Record transaction
    
    return true;
  } catch (error) {
    // Handle purchase error
    return false;
  }
}, [state.offerings]);
```

### 2. Purchase Validation

Implement server-side purchase validation:

```sql
-- Add to supabase-schema.sql
CREATE OR REPLACE FUNCTION public.validate_purchase(
    user_uuid UUID,
    transaction_id TEXT,
    product_id TEXT,
    purchase_token TEXT
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Validate with RevenueCat webhook or API
    -- This should be implemented as a secure server function
    -- For now, we'll trust the client (not recommended for production)
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 3. Webhook Integration

Set up RevenueCat webhooks to handle:
- Purchase events
- Subscription renewals
- Refunds
- Subscription cancellations

```typescript
// Example webhook handler (would be implemented in your backend)
export async function handleRevenueCatWebhook(event: any) {
  switch (event.type) {
    case 'INITIAL_PURCHASE':
      await processPurchase(event);
      break;
    case 'RENEWAL':
      await processRenewal(event);
      break;
    case 'CANCELLATION':
      await processCancellation(event);
      break;
    // Handle other event types
  }
}
```

## Testing

### 1. Sandbox Testing

1. **iOS**: Use sandbox Apple ID for testing
2. **Android**: Use test accounts in Google Play Console
3. **RevenueCat**: Use test mode in dashboard

### 2. Test Scenarios

- Successful purchases
- Failed purchases
- Network interruptions
- Purchase restoration
- Subscription management

## Security Best Practices

### 1. Server-Side Validation

- Always validate purchases on your server
- Use RevenueCat webhooks for real-time updates
- Never trust client-side purchase verification alone

### 2. User Identification

- Use consistent user IDs across RevenueCat and Supabase
- Implement proper user authentication
- Handle anonymous users appropriately

### 3. Error Handling

- Graceful degradation when purchases fail
- Proper error messages for users
- Logging for debugging and support

## Production Checklist

- [ ] Products configured in app stores
- [ ] RevenueCat dashboard properly set up
- [ ] Webhook endpoints implemented and tested
- [ ] Purchase validation implemented server-side
- [ ] Error handling and logging in place
- [ ] Sandbox testing completed
- [ ] Production testing with real money (small amounts)
- [ ] Customer support processes for purchase issues
- [ ] Analytics tracking for purchase events
- [ ] Compliance with app store guidelines

## Troubleshooting

### Common Issues

1. **Products not loading**
   - Check product IDs match exactly
   - Verify products are approved in app store
   - Ensure RevenueCat configuration is correct

2. **Purchases failing**
   - Check network connectivity
   - Verify user authentication
   - Review RevenueCat logs

3. **Restore not working**
   - Ensure user is signed in to same app store account
   - Check RevenueCat user ID consistency
   - Verify purchase history in RevenueCat dashboard

### Support Resources

- RevenueCat Documentation: [docs.revenuecat.com](https://docs.revenuecat.com)
- React Native Purchases: [github.com/RevenueCat/react-native-purchases](https://github.com/RevenueCat/react-native-purchases)
- RevenueCat Community: [community.revenuecat.com](https://community.revenuecat.com)

## Current Implementation Status

The current implementation includes:
- ✅ Mock purchase flow
- ✅ UI components for purchases
- ✅ Database schema for purchase tracking
- ✅ Basic error handling
- ❌ Real RevenueCat integration (mock only)
- ❌ Server-side validation
- ❌ Webhook handling
- ❌ Production testing

To complete the integration, replace the mock implementation in `usePurchases.ts` with real RevenueCat calls and implement server-side validation.
