{"name": "frontend", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint .", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.23", "@reduxjs/toolkit": "^2.2.3", "@supabase/supabase-js": "^2.50.0", "axios": "^1.6.8", "expo": "~53.0.11", "expo-ads-admob": "^8.4.0", "expo-constants": "~17.1.6", "expo-device": "^5.5.1", "expo-font": "^13.3.1", "expo-linking": "^7.1.5", "expo-router": "^5.1.0", "expo-secure-store": "^12.5.1", "expo-splash-screen": "^0.30.9", "expo-status-bar": "~2.2.3", "lottie-react-native": "^6.4.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "^2.16.1", "react-native-paper": "^5.11.0", "react-native-reanimated": "^3.8.0", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.30.0", "react-native-url-polyfill": "^2.0.0", "react-redux": "^9.1.2", "source-map": "^0.6.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.57.0", "jest": "^29.7.0", "typescript": "~5.8.3"}, "private": true}