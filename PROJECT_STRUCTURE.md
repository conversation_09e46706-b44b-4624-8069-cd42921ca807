# 📁 Project Structure

Complete overview of the Jackpot Party Casino project structure and file organization.

## 🏗️ Root Directory

```
jackpot-party-casino/
├── 📱 app/                          # Expo Router screens
├── 🧩 src/                          # Source code
├── 📚 docs/                         # Documentation
├── 🧪 __tests__/                    # Global tests
├── 🔧 Configuration files
└── 📄 Documentation files
```

## 📱 App Directory (Expo Router)

```
app/
├── index.tsx                        # Root screen with privacy/tracking
├── auth.tsx                         # Authentication screen
├── lobby.tsx                        # Main lobby/home screen
├── daily-bonus.tsx                  # Daily bonus wheel screen
├── shop.tsx                         # In-app purchase shop
├── settings.tsx                     # App settings screen
├── slot/
│   └── [gameId].tsx                 # Dynamic slot machine screen
└── _layout.tsx                      # Root layout (if needed)
```

## 🧩 Source Code Structure

```
src/
├── components/                      # Reusable UI components
│   ├── common/                      # Generic components
│   │   ├── BalanceDisplay.tsx       # Coins/gems/spins display
│   │   ├── LevelProgress.tsx        # XP and level indicator
│   │   ├── GameTile.tsx             # Game selection cards
│   │   ├── LoadingScreen.tsx        # Loading state component
│   │   ├── Button.tsx               # Reusable button component
│   │   ├── ScreenContainer.tsx      # Screen wrapper
│   │   ├── PurchaseModal.tsx        # IAP purchase interface
│   │   └── NewQuestBanner.tsx       # Quest notifications
│   ├── slot/                        # Slot machine components
│   │   ├── SlotMachine.tsx          # Main slot machine
│   │   ├── AnimatedReel.tsx         # Individual spinning reel
│   │   ├── SpinButton.tsx           # Spin control button
│   │   ├── BetControl.tsx           # Bet amount selection
│   │   ├── AutoSpinToggle.tsx       # Auto-spin controls
│   │   └── WinDisplay.tsx           # Win celebration
│   ├── bonus/                       # Daily bonus components
│   │   ├── WheelOfFortune.tsx       # SVG spinning wheel
│   │   └── StreakReward.tsx         # Streak progress display
│   └── popups/                      # Modal and popup components
│       ├── PrivacyConsent.tsx       # GDPR privacy consent
│       ├── AppTrackingPermission.tsx # iOS tracking permission
│       └── TutorialOverlay.tsx      # Interactive tutorial
├── hooks/                           # Custom React hooks
│   ├── index.ts                     # Hook exports
│   ├── useAuth.ts                   # Authentication state
│   ├── useCoins.ts                  # Coin management
│   ├── useLevel.ts                  # XP and level progression
│   ├── useSlotEngine.ts             # Slot machine logic
│   ├── usePurchases.ts              # In-app purchases
│   ├── useQuests.ts                 # Quest system
│   ├── useLeaderboards.ts           # Competitive features
│   ├── useRealTimeUpdates.ts        # Live notifications
│   ├── useAnalytics.ts              # Event tracking
│   ├── useNotifications.ts          # Push notifications
│   ├── useWallet.ts                 # Legacy wallet hook
│   ├── useGame.ts                   # Legacy game hook
│   └── useDeviceId.ts               # Device identification
├── contexts/                        # React contexts
│   └── AuthContext.tsx              # Authentication context
├── utils/                           # Utility functions
│   └── supabase.ts                  # Supabase client setup
├── constants/                       # App constants
│   └── colors.ts                    # Color scheme and shadows
└── test/                           # Test utilities
    └── setup.ts                     # Jest test configuration
```

## 📚 Documentation

```
docs/
├── API.md                          # Database schema and API docs
├── TROUBLESHOOTING.md              # Common issues and solutions
└── SECURITY.md                     # Security implementation guide
```

## 🔧 Configuration Files

```
Root Directory:
├── .env.example                    # Environment variables template
├── .env                           # Environment variables (gitignored)
├── .gitignore                     # Git ignore patterns
├── .eslintrc.js                   # ESLint configuration
├── .prettierrc.js                 # Prettier formatting rules
├── app.config.js                  # Expo app configuration
├── babel.config.js                # Babel transpilation config
├── eas.json                       # EAS Build configuration
├── jest.config.js                 # Jest testing configuration
├── metro.config.js                # Metro bundler config
├── package.json                   # Dependencies and scripts
├── package-lock.json              # Dependency lock file
├── tsconfig.json                  # TypeScript configuration
└── supabase-schema.sql            # Database schema
```

## 📄 Documentation Files

```
Root Directory:
├── README.md                      # Main project documentation
├── CHANGELOG.md                   # Version history and changes
├── CONTRIBUTING.md                # Contribution guidelines
├── DEPLOYMENT.md                  # Deployment instructions
├── REVENUECAT_SETUP.md           # RevenueCat integration guide
├── LICENSE                        # MIT license
└── PROJECT_STRUCTURE.md          # This file
```

## 🧪 Testing Structure

```
Testing Files:
├── jest.config.js                 # Jest configuration
├── src/test/setup.ts              # Test environment setup
├── src/hooks/__tests__/           # Hook unit tests
│   └── useCoins.test.ts
├── src/components/common/__tests__/ # Component tests
│   └── BalanceDisplay.test.tsx
└── __tests__/                     # Global integration tests
```

## 🚀 Build and Deployment

```
Build Artifacts (gitignored):
├── .expo/                         # Expo build cache
├── dist/                          # Distribution builds
├── web-build/                     # Web build output
├── coverage/                      # Test coverage reports
└── node_modules/                  # Dependencies
```

## 📊 Key File Relationships

### Authentication Flow
```
app/index.tsx → AuthContext → useAuth → Supabase Auth
```

### Slot Machine Flow
```
app/slot/[gameId].tsx → useSlotEngine → Supabase game_sessions
```

### Economy System
```
useCoins ↔ Supabase users table
useLevel ↔ XP calculation functions
useQuests ↔ Supabase quests table
```

### Real-time Features
```
useRealTimeUpdates → Supabase subscriptions → UI updates
```

## 🎯 Component Hierarchy

### Main App Structure
```
App
├── PrivacyConsent (popup)
├── AppTrackingPermission (popup)
├── AuthContext.Provider
│   ├── AuthScreen (if not authenticated)
│   └── LobbyScreen (if authenticated)
│       ├── BalanceDisplay
│       ├── LevelProgress
│       ├── GameTile[]
│       ├── NewQuestBanner
│       ├── PurchaseModal
│       └── TutorialOverlay
```

### Slot Machine Structure
```
SlotScreen
├── BalanceDisplay
├── AnimatedReel[] (5 reels)
├── WinDisplay
├── BetControl
├── SpinButton
└── AutoSpinToggle
```

## 🔄 Data Flow

### User Authentication
```
User Input → AuthScreen → Supabase Auth → AuthContext → App State
```

### Slot Machine Spin
```
SpinButton → useSlotEngine → Database → useCoins → UI Update
```

### Real-time Updates
```
Database Change → Supabase Subscription → Hook Update → Component Re-render
```

### Analytics Tracking
```
User Action → useAnalytics → Supabase analytics_events → Dashboard
```

## 📱 Screen Navigation

```
Index (Privacy/Tracking)
├── Auth Screen
└── Lobby Screen
    ├── Slot Machine Screen
    ├── Daily Bonus Screen
    ├── Shop Screen
    └── Settings Screen
```

## 🎨 Styling Architecture

### Color System
```
src/constants/colors.ts
├── Primary colors (brand)
├── Semantic colors (success, error, warning)
├── Text colors (primary, secondary)
├── Background colors
└── Shadow definitions
```

### Component Styling
- Each component has its own StyleSheet
- Consistent spacing and typography
- Responsive design patterns
- Dark theme support

## 🔧 Development Workflow

### Local Development
```bash
npm start                          # Start Expo dev server
npm test                          # Run unit tests
npm run lint                      # Check code quality
npm run type-check               # TypeScript validation
```

### Build Process
```bash
eas build --profile development   # Development build
eas build --profile preview      # Preview build
eas build --profile production   # Production build
```

### Deployment
```bash
eas submit --platform ios        # Submit to App Store
eas submit --platform android    # Submit to Google Play
```

This structure provides a scalable, maintainable codebase that follows React Native and Expo best practices while maintaining clear separation of concerns and easy navigation for developers. 🚀
