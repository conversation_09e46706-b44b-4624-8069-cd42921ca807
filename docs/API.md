# API Documentation

This document describes the API structure and database interactions for Jackpot Party Casino.

## 🗄️ Database Schema

### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email TEXT NOT NULL,
    username TEXT UNIQUE,
    avatar_url TEXT,
    push_token TEXT,
    coins BIGINT DEFAULT 1000,
    spins INTEGER DEFAULT 10,
    gems INTEGER DEFAULT 0,
    xp BIGINT DEFAULT 0,
    level INTEGER DEFAULT 1,
    daily_streak INTEGER DEFAULT 0,
    last_daily_bonus TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Game Sessions Table
```sql
CREATE TABLE game_sessions (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    game_type TEXT NOT NULL,
    bet_amount INTEGER NOT NULL,
    payout INTEGER DEFAULT 0,
    result_data JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Quests Table
```sql
CREATE TABLE quests (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    quest_type TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    target_value INTEGER NOT NULL,
    current_value INTEGER DEFAULT 0,
    reward_coins INTEGER DEFAULT 0,
    reward_xp INTEGER DEFAULT 0,
    reward_gems INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

## 🔧 Database Functions

### Claim Daily Bonus
```sql
SELECT * FROM claim_daily_bonus(user_uuid);
```

**Parameters:**
- `user_uuid`: UUID of the user claiming bonus

**Returns:**
- `bonus_amount`: Amount of coins awarded
- `new_streak`: Updated daily streak count
- `level_up`: Boolean indicating if user leveled up

### Add Coins
```sql
SELECT * FROM add_coins(user_uuid, amount);
```

**Parameters:**
- `user_uuid`: UUID of the user
- `amount`: Number of coins to add

**Returns:**
- `new_balance`: Updated coin balance
- `success`: Boolean indicating success

### Subtract Coins
```sql
SELECT * FROM subtract_coins(user_uuid, amount);
```

**Parameters:**
- `user_uuid`: UUID of the user
- `amount`: Number of coins to subtract

**Returns:**
- `new_balance`: Updated coin balance
- `success`: Boolean indicating if transaction was successful

## 📊 Analytics Events

### Event Types
- `spin` - Slot machine spin
- `win` - Slot machine win
- `purchase` - In-app purchase
- `level_up` - User level increase
- `daily_collect` - Daily bonus collection
- `app_open` - App launch
- `session_start` - New session
- `quest_complete` - Quest completion

### Event Data Structure
```typescript
interface AnalyticsEvent {
  event_type: string;
  event_data: Record<string, any>;
  user_id?: string;
  session_id: string;
  timestamp: string;
  app_version: string;
  platform: string;
  device_info: Record<string, any>;
}
```

### Example Events

#### Spin Event
```json
{
  "event_type": "spin",
  "event_data": {
    "bet_amount": 25,
    "game_type": "classic"
  },
  "user_id": "user-uuid",
  "session_id": "session-123",
  "timestamp": "2024-01-01T12:00:00Z",
  "app_version": "1.0.0",
  "platform": "ios",
  "device_info": {
    "platform": "ios",
    "version": "17.0",
    "deviceName": "iPhone 15 Pro"
  }
}
```

#### Win Event
```json
{
  "event_type": "win",
  "event_data": {
    "win_amount": 100,
    "bet_amount": 25,
    "game_type": "classic",
    "is_jackpot": false,
    "multiplier": 4
  }
}
```

## 🔔 Real-time Subscriptions

### Quest Updates
```typescript
supabase
  .channel(`quests_${userId}`)
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'quests',
    filter: `user_id=eq.${userId}`
  }, handleQuestUpdate)
  .subscribe();
```

### User Profile Updates
```typescript
supabase
  .channel(`user_${userId}`)
  .on('postgres_changes', {
    event: 'UPDATE',
    schema: 'public',
    table: 'users',
    filter: `id=eq.${userId}`
  }, handleUserUpdate)
  .subscribe();
```

### Leaderboard Updates
```typescript
supabase
  .channel('leaderboards')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'leaderboards'
  }, handleLeaderboardUpdate)
  .subscribe();
```

## 🎰 Slot Machine Algorithm

### Symbol Weights
```typescript
const SYMBOL_WEIGHTS = {
  '🍒': 30,  // Cherry - Most common
  '🍋': 25,  // Lemon
  '🔔': 20,  // Bell
  '7️⃣': 15,  // Seven
  '💎': 8,   // Diamond
  '🎰': 2,   // Jackpot - Rarest
};
```

### Payline Configuration
```typescript
const PAYLINES = [
  [0, 1, 2], // Top row
  [3, 4, 5], // Middle row
  [6, 7, 8], // Bottom row
  [0, 4, 8], // Diagonal top-left to bottom-right
  [2, 4, 6], // Diagonal top-right to bottom-left
  [0, 3, 6], // Left column
  [1, 4, 7], // Middle column
  [2, 5, 8], // Right column
  [3, 4, 2], // V-shape
];
```

### RTP Calculation
The slot machine maintains a 95% Return to Player (RTP) rate:
- **Expected payout**: 95% of total bets over time
- **House edge**: 5%
- **Variance**: Medium to high for engaging gameplay

## 🛒 Purchase Validation

### RevenueCat Integration
```typescript
interface PurchasePackage {
  identifier: string;
  product: {
    title: string;
    description: string;
    priceString: string;
  };
}
```

### Purchase Flow
1. User selects package
2. RevenueCat handles payment
3. Webhook validates purchase
4. Coins added to user account
5. Transaction recorded in database

## 🔒 Security

### Row Level Security Policies
```sql
-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Game sessions are user-specific
CREATE POLICY "Users can view own sessions" ON game_sessions
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sessions" ON game_sessions
  FOR INSERT WITH CHECK (auth.uid() = user_id);
```

### Authentication
- **Supabase Auth** for user management
- **JWT tokens** for session management
- **Row Level Security** for data protection
- **Environment variables** for sensitive configuration

## 📱 Client-Side Hooks

### useCoins
```typescript
const { balance, loading, add, subtract, canAfford } = useCoins();
```

### useSlotEngine
```typescript
const { spin, spinning, lastResult } = useSlotEngine();
```

### useQuests
```typescript
const { 
  quests, 
  loading, 
  updateQuestProgress, 
  getActiveQuests 
} = useQuests();
```

### useAnalytics
```typescript
const { 
  trackSpin, 
  trackWin, 
  trackPurchase, 
  trackLevelUp 
} = useAnalytics();
```

## 🚀 Performance Considerations

### Database Optimization
- **Indexes** on frequently queried columns
- **Connection pooling** for concurrent users
- **Query optimization** with proper joins
- **Real-time subscriptions** with filters

### Client Optimization
- **Local caching** with AsyncStorage
- **Optimistic updates** for better UX
- **Efficient re-renders** with React optimization
- **Image optimization** and lazy loading

### Monitoring
- **Error tracking** with Supabase logs
- **Performance metrics** with custom analytics
- **Real-time monitoring** of database performance
- **User behavior analysis** with event tracking
