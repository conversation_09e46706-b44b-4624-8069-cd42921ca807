# 🔧 Troubleshooting Guide

Common issues and solutions for Jackpot Party Casino development and deployment.

## 🚀 Development Issues

### Metro Bundler Problems

#### Issue: Metro bundler won't start or crashes
```bash
# Clear Metro cache
npx expo start --clear

# Reset Metro completely
rm -rf node_modules/.cache
npx expo start --clear
```

#### Issue: "Unable to resolve module" errors
```bash
# Clear all caches and reinstall
rm -rf node_modules package-lock.json
npm install
npx expo start --clear
```

#### Issue: TypeScript errors in Metro
```bash
# Check TypeScript configuration
npx tsc --noEmit

# Restart TypeScript service in VS Code
# Cmd+Shift+P -> "TypeScript: Restart TS Server"
```

### Supabase Connection Issues

#### Issue: "Invalid API key" or connection errors
1. **Check environment variables**:
   ```bash
   # Verify .env file exists and has correct values
   cat .env
   ```

2. **Verify Supabase project settings**:
   - Project URL format: `https://your-project.supabase.co`
   - Anon key should start with `eyJ`

3. **Test connection**:
   ```typescript
   import { supabase } from './src/utils/supabase';
   
   // Test in a component
   useEffect(() => {
     supabase.from('users').select('count').then(console.log);
   }, []);
   ```

#### Issue: RLS (Row Level Security) blocking queries
1. **Check RLS policies**:
   ```sql
   -- View existing policies
   SELECT * FROM pg_policies WHERE tablename = 'users';
   ```

2. **Temporarily disable RLS for testing**:
   ```sql
   ALTER TABLE users DISABLE ROW LEVEL SECURITY;
   -- Remember to re-enable: ALTER TABLE users ENABLE ROW LEVEL SECURITY;
   ```

3. **Debug auth context**:
   ```typescript
   const { user } = useAuthContext();
   console.log('Current user:', user);
   ```

### Authentication Problems

#### Issue: User not persisting after app restart
1. **Check AsyncStorage**:
   ```typescript
   import AsyncStorage from '@react-native-async-storage/async-storage';
   
   // Debug stored session
   AsyncStorage.getItem('supabase.auth.token').then(console.log);
   ```

2. **Verify auth initialization**:
   ```typescript
   // In AuthContext, ensure proper initialization
   useEffect(() => {
     supabase.auth.getSession().then(({ data: { session } }) => {
       setSession(session);
       setLoading(false);
     });
   }, []);
   ```

#### Issue: Sign-up/Sign-in not working
1. **Check Supabase auth settings**:
   - Email confirmation enabled/disabled
   - Allowed domains configuration
   - Auth providers setup

2. **Debug auth errors**:
   ```typescript
   const handleSignIn = async (email, password) => {
     const { data, error } = await supabase.auth.signInWithPassword({
       email,
       password,
     });
     
     if (error) {
       console.error('Auth error:', error);
       Alert.alert('Error', error.message);
     }
   };
   ```

## 📱 Device-Specific Issues

### iOS Issues

#### Issue: App crashes on iOS simulator
1. **Check iOS version compatibility**:
   - Ensure iOS 13+ for Expo SDK 53
   - Update Xcode and simulators

2. **Reset iOS simulator**:
   ```bash
   # Reset simulator
   xcrun simctl erase all
   ```

3. **Check console logs**:
   - Open Console.app on Mac
   - Filter by device name
   - Look for crash logs

#### Issue: Push notifications not working on iOS
1. **Check capabilities**:
   ```json
   // In app.config.js
   {
     "expo": {
       "ios": {
         "capabilities": {
           "push-notifications": true
         }
       }
     }
   }
   ```

2. **Verify certificates**:
   - Apple Developer account setup
   - Push notification certificates
   - Provisioning profiles

### Android Issues

#### Issue: App won't install on Android
1. **Check Android version**:
   - Minimum Android 6.0 (API 23)
   - Target latest stable version

2. **Clear Android cache**:
   ```bash
   # Clear Android build cache
   cd android
   ./gradlew clean
   cd ..
   ```

3. **Check permissions**:
   ```json
   // In app.config.js
   {
     "expo": {
       "android": {
         "permissions": [
           "INTERNET",
           "VIBRATE",
           "RECEIVE_BOOT_COMPLETED"
         ]
       }
     }
   }
   ```

## 🧪 Testing Issues

### Jest Test Failures

#### Issue: Tests failing with module resolution errors
1. **Check Jest configuration**:
   ```javascript
   // jest.config.js
   module.exports = {
     preset: 'jest-expo',
     transformIgnorePatterns: [
       'node_modules/(?!(jest-)?@?react-native|@react-native-community|expo|@expo|@unimodules)',
     ],
   };
   ```

2. **Update test setup**:
   ```typescript
   // src/test/setup.ts
   import 'react-native-gesture-handler/jestSetup';
   
   // Mock problematic modules
   jest.mock('react-native-reanimated', () => {
     const Reanimated = require('react-native-reanimated/mock');
     Reanimated.default.call = () => {};
     return Reanimated;
   });
   ```

#### Issue: Async tests timing out
```typescript
// Increase timeout for async tests
describe('Async operations', () => {
  it('should complete within time limit', async () => {
    // Set longer timeout
    jest.setTimeout(10000);
    
    await someAsyncOperation();
  });
});
```

### Component Testing Issues

#### Issue: Components not rendering in tests
1. **Check test providers**:
   ```typescript
   import { render } from '@testing-library/react-native';
   import { AuthProvider } from '../contexts/AuthContext';
   
   const renderWithProviders = (component) => {
     return render(
       <AuthProvider>
         {component}
       </AuthProvider>
     );
   };
   ```

2. **Mock navigation**:
   ```typescript
   jest.mock('@react-navigation/native', () => ({
     useNavigation: () => ({
       navigate: jest.fn(),
       goBack: jest.fn(),
     }),
   }));
   ```

## 🏗️ Build Issues

### EAS Build Failures

#### Issue: Build failing on EAS
1. **Check build logs**:
   ```bash
   eas build:list
   eas build:view [build-id]
   ```

2. **Common fixes**:
   ```bash
   # Update EAS CLI
   npm install -g eas-cli@latest
   
   # Clear EAS cache
   eas build --clear-cache
   ```

3. **Check dependencies**:
   ```json
   // Ensure compatible versions in package.json
   {
     "dependencies": {
       "expo": "~53.0.0",
       "react-native": "0.73.0"
     }
   }
   ```

#### Issue: iOS build certificate errors
1. **Regenerate certificates**:
   ```bash
   eas credentials
   # Select iOS -> Distribution Certificate -> Regenerate
   ```

2. **Check Apple Developer account**:
   - Valid membership
   - Correct team ID
   - Provisioning profiles

#### Issue: Android build signing errors
1. **Check keystore**:
   ```bash
   eas credentials
   # Select Android -> Keystore -> Generate new
   ```

2. **Verify build configuration**:
   ```json
   // eas.json
   {
     "build": {
       "production": {
         "android": {
           "buildType": "app-bundle"
         }
       }
     }
   }
   ```

## 🚀 Deployment Issues

### App Store Submission

#### Issue: iOS app rejected
1. **Common rejection reasons**:
   - Missing privacy policy
   - Inappropriate age rating
   - In-app purchase issues
   - Metadata violations

2. **Check App Store guidelines**:
   - Review Apple's guidelines
   - Ensure compliance with gambling simulation rules
   - Proper age rating (17+ for simulated gambling)

#### Issue: Google Play rejection
1. **Common issues**:
   - Target API level too low
   - Missing content rating
   - Policy violations
   - Incomplete store listing

2. **Verify requirements**:
   - Target Android API 34+
   - Complete content rating questionnaire
   - Proper app category selection

### Performance Issues

#### Issue: App running slowly
1. **Profile performance**:
   ```bash
   # Use Flipper for debugging
   npx expo start --dev-client
   ```

2. **Optimize images**:
   ```bash
   # Compress images
   npx expo optimize
   ```

3. **Check memory usage**:
   - Monitor with Xcode Instruments (iOS)
   - Use Android Studio Profiler (Android)

#### Issue: Database queries slow
1. **Add indexes**:
   ```sql
   CREATE INDEX idx_game_sessions_user_created 
   ON game_sessions(user_id, created_at DESC);
   ```

2. **Optimize queries**:
   ```typescript
   // Use select() to limit columns
   const { data } = await supabase
     .from('users')
     .select('id, coins, level')
     .eq('id', userId)
     .single();
   ```

## 📞 Getting Help

### Debug Information to Collect

When reporting issues, include:

1. **Environment details**:
   ```bash
   npx expo doctor
   npm --version
   node --version
   ```

2. **Error logs**:
   - Metro bundler output
   - Device console logs
   - Supabase logs

3. **Reproduction steps**:
   - Exact steps to reproduce
   - Expected vs actual behavior
   - Screenshots/videos if applicable

### Support Channels

1. **GitHub Issues**: For bugs and feature requests
2. **GitHub Discussions**: For questions and help
3. **Expo Forums**: For Expo-specific issues
4. **Supabase Discord**: For backend issues

### Emergency Fixes

#### Critical production issue
1. **Rollback deployment**:
   ```bash
   # Revert to previous version
   eas submit --platform all --latest
   ```

2. **Hotfix process**:
   ```bash
   git checkout main
   git checkout -b hotfix/critical-fix
   # Make minimal fix
   git commit -m "hotfix: critical issue"
   git push origin hotfix/critical-fix
   # Create PR and deploy immediately
   ```

Remember: Always test fixes in development before deploying to production! 🚀
