# 🔒 Security Guide

Security best practices and implementation details for Jackpot Party Casino.

## 🛡️ Security Overview

This app implements multiple layers of security to protect user data and prevent fraud:

- **Authentication**: Supabase Auth with JWT tokens
- **Authorization**: Row Level Security (RLS) policies
- **Data Protection**: Encrypted connections and secure storage
- **Privacy**: GDPR compliance and user consent
- **Fraud Prevention**: Server-side validation and monitoring

## 🔐 Authentication Security

### Supabase Auth Implementation

```typescript
// Secure authentication setup
const { data, error } = await supabase.auth.signInWithPassword({
  email: email.toLowerCase().trim(),
  password: password,
});

// Always validate auth state
const { user, session } = useAuthContext();
if (!user || !session) {
  // Redirect to login
  return;
}
```

### Session Management

```typescript
// Automatic session refresh
useEffect(() => {
  const { data: { subscription } } = supabase.auth.onAuthStateChange(
    (event, session) => {
      if (event === 'SIGNED_OUT' || !session) {
        // Clear local data
        AsyncStorage.clear();
        setUser(null);
      }
    }
  );

  return () => subscription.unsubscribe();
}, []);
```

### Password Security

- **Minimum requirements**: 8 characters, mixed case, numbers
- **Hashing**: Handled by Supabase (bcrypt)
- **Reset flow**: Secure email-based password reset
- **Rate limiting**: Built into Supabase Auth

## 🔒 Data Protection

### Row Level Security (RLS)

All database tables use RLS to ensure users can only access their own data:

```sql
-- Users can only see their own profile
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

-- Users can only update their own profile
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Game sessions are user-specific
CREATE POLICY "Users can view own sessions" ON game_sessions
  FOR SELECT USING (auth.uid() = user_id);

-- Quests are user-specific
CREATE POLICY "Users can manage own quests" ON quests
  FOR ALL USING (auth.uid() = user_id);
```

### Database Security

```sql
-- Prevent direct access to sensitive functions
REVOKE ALL ON FUNCTION claim_daily_bonus FROM PUBLIC;
GRANT EXECUTE ON FUNCTION claim_daily_bonus TO authenticated;

-- Audit trail for important operations
CREATE TABLE audit_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID,
  action TEXT NOT NULL,
  table_name TEXT NOT NULL,
  old_values JSONB,
  new_values JSONB,
  timestamp TIMESTAMPTZ DEFAULT NOW()
);
```

### Environment Variables

```bash
# Never commit these to version control
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# Use different keys for different environments
EXPO_PUBLIC_SUPABASE_URL_DEV=https://dev-project.supabase.co
EXPO_PUBLIC_SUPABASE_URL_PROD=https://prod-project.supabase.co
```

## 💰 Financial Security

### In-App Purchase Validation

```typescript
// Server-side purchase validation
const validatePurchase = async (receipt: string, productId: string) => {
  try {
    // Validate with RevenueCat
    const validation = await RevenueCat.validateReceipt(receipt);
    
    if (!validation.valid) {
      throw new Error('Invalid purchase receipt');
    }
    
    // Record in database with validation status
    const { error } = await supabase
      .from('purchases')
      .insert({
        user_id: user.id,
        product_id: productId,
        receipt_data: receipt,
        validation_status: 'verified',
        amount: validation.amount,
      });
      
    return !error;
  } catch (error) {
    console.error('Purchase validation failed:', error);
    return false;
  }
};
```

### Anti-Fraud Measures

```typescript
// Rate limiting for sensitive operations
const rateLimiter = {
  spins: new Map(),
  purchases: new Map(),
  
  checkSpinRate: (userId: string) => {
    const now = Date.now();
    const userSpins = this.spins.get(userId) || [];
    
    // Remove spins older than 1 minute
    const recentSpins = userSpins.filter(time => now - time < 60000);
    
    // Max 60 spins per minute
    if (recentSpins.length >= 60) {
      throw new Error('Rate limit exceeded');
    }
    
    recentSpins.push(now);
    this.spins.set(userId, recentSpins);
  }
};
```

### Coin Balance Protection

```sql
-- Prevent negative balances
ALTER TABLE users ADD CONSTRAINT positive_coins CHECK (coins >= 0);

-- Atomic coin operations
CREATE OR REPLACE FUNCTION subtract_coins_safe(
  user_uuid UUID,
  amount INTEGER
) RETURNS BOOLEAN AS $$
DECLARE
  current_balance BIGINT;
BEGIN
  -- Lock the user row
  SELECT coins INTO current_balance 
  FROM users 
  WHERE id = user_uuid 
  FOR UPDATE;
  
  -- Check if sufficient balance
  IF current_balance < amount THEN
    RETURN FALSE;
  END IF;
  
  -- Subtract coins
  UPDATE users 
  SET coins = coins - amount,
      updated_at = NOW()
  WHERE id = user_uuid;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 🔍 Privacy Protection

### GDPR Compliance

```typescript
// Privacy consent implementation
const PrivacyConsent = () => {
  const handleAccept = async () => {
    await AsyncStorage.setItem('privacy_consent', JSON.stringify({
      accepted: true,
      timestamp: new Date().toISOString(),
      version: '1.0'
    }));
    
    // Enable analytics and tracking
    await Analytics.setEnabled(true);
  };
  
  const handleDecline = async () => {
    await AsyncStorage.setItem('privacy_consent', JSON.stringify({
      accepted: false,
      timestamp: new Date().toISOString(),
      version: '1.0'
    }));
    
    // Disable analytics and tracking
    await Analytics.setEnabled(false);
  };
};
```

### Data Minimization

```typescript
// Only collect necessary data
const trackEvent = async (eventType: string, data: any) => {
  // Remove PII from analytics
  const sanitizedData = {
    ...data,
    // Remove email, name, etc.
    user_id: user?.id, // Only use UUID
    timestamp: new Date().toISOString(),
  };
  
  await supabase.from('analytics_events').insert({
    event_type: eventType,
    event_data: sanitizedData,
  });
};
```

### Data Retention

```sql
-- Automatic cleanup of old data
CREATE OR REPLACE FUNCTION cleanup_old_data() RETURNS void AS $$
BEGIN
  -- Delete analytics events older than 2 years
  DELETE FROM analytics_events 
  WHERE timestamp < NOW() - INTERVAL '2 years';
  
  -- Delete old game sessions (keep 1 year)
  DELETE FROM game_sessions 
  WHERE created_at < NOW() - INTERVAL '1 year';
  
  -- Archive completed quests older than 30 days
  UPDATE quests 
  SET archived = true 
  WHERE completed = true 
    AND updated_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- Schedule cleanup (run daily)
SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data();');
```

## 🚨 Security Monitoring

### Error Tracking

```typescript
// Secure error logging
const logSecurityEvent = async (event: string, details: any) => {
  // Don't log sensitive data
  const sanitizedDetails = {
    ...details,
    // Remove passwords, tokens, etc.
    timestamp: new Date().toISOString(),
    user_agent: navigator.userAgent,
    ip_address: 'redacted', // Don't store IP addresses
  };
  
  await supabase.from('security_events').insert({
    event_type: event,
    details: sanitizedDetails,
  });
};

// Monitor failed login attempts
const handleLoginFailure = async (email: string, error: any) => {
  await logSecurityEvent('login_failure', {
    email: email.toLowerCase(),
    error_code: error.code,
    // Don't log the actual error message (might contain sensitive info)
  });
};
```

### Anomaly Detection

```sql
-- Detect unusual spending patterns
CREATE OR REPLACE FUNCTION detect_spending_anomaly(user_uuid UUID) 
RETURNS BOOLEAN AS $$
DECLARE
  recent_spending NUMERIC;
  avg_spending NUMERIC;
BEGIN
  -- Calculate spending in last hour
  SELECT COALESCE(SUM(amount), 0) INTO recent_spending
  FROM purchases 
  WHERE user_id = user_uuid 
    AND created_at > NOW() - INTERVAL '1 hour';
  
  -- Calculate average daily spending
  SELECT COALESCE(AVG(daily_total), 0) INTO avg_spending
  FROM (
    SELECT DATE(created_at), SUM(amount) as daily_total
    FROM purchases 
    WHERE user_id = user_uuid 
      AND created_at > NOW() - INTERVAL '30 days'
    GROUP BY DATE(created_at)
  ) daily_totals;
  
  -- Flag if recent spending is 10x average
  RETURN recent_spending > (avg_spending * 10);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 🔧 Security Configuration

### Supabase Security Settings

```sql
-- Enable audit logging
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Set connection limits
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';

-- Enable SSL only
ALTER SYSTEM SET ssl = on;
ALTER SYSTEM SET ssl_prefer_server_ciphers = on;
```

### App Security Headers

```typescript
// Configure secure HTTP headers
const securityHeaders = {
  'Strict-Transport-Security': 'max-age=********; includeSubDomains',
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
};
```

## 🚀 Security Checklist

### Pre-Launch Security Review

- [ ] **Authentication**
  - [ ] Strong password requirements
  - [ ] Session timeout configured
  - [ ] Rate limiting enabled
  - [ ] Account lockout after failed attempts

- [ ] **Authorization**
  - [ ] RLS policies tested
  - [ ] API endpoints secured
  - [ ] Admin functions protected
  - [ ] User permissions validated

- [ ] **Data Protection**
  - [ ] Encryption in transit (HTTPS)
  - [ ] Encryption at rest (Supabase)
  - [ ] Sensitive data masked in logs
  - [ ] PII handling compliant

- [ ] **Privacy**
  - [ ] Privacy policy updated
  - [ ] Consent mechanisms working
  - [ ] Data retention policies set
  - [ ] User data export/deletion

- [ ] **Financial Security**
  - [ ] Purchase validation working
  - [ ] Fraud detection active
  - [ ] Balance protection enabled
  - [ ] Transaction logging complete

- [ ] **Monitoring**
  - [ ] Security event logging
  - [ ] Anomaly detection active
  - [ ] Error tracking configured
  - [ ] Incident response plan ready

## 🆘 Incident Response

### Security Incident Procedure

1. **Immediate Response**
   - Assess severity and impact
   - Contain the incident
   - Preserve evidence

2. **Investigation**
   - Analyze logs and data
   - Identify root cause
   - Document findings

3. **Recovery**
   - Implement fixes
   - Restore normal operations
   - Monitor for recurrence

4. **Post-Incident**
   - Update security measures
   - Notify affected users if required
   - Review and improve procedures

### Emergency Contacts

- **Security Team**: <EMAIL>
- **Supabase Support**: <EMAIL>
- **Legal Team**: <EMAIL>

Remember: Security is an ongoing process, not a one-time setup! 🔒
