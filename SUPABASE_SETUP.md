# Supabase Setup Guide for Jackpot Party Casino

## Prerequisites

1. Create a Supabase account at [supabase.com](https://supabase.com)
2. Create a new project in your Supabase dashboard

## Database Setup

### Step 1: Run the Schema

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `supabase-schema.sql`
4. Click "Run" to execute the schema

### Step 2: Configure Environment Variables

1. In your Supabase project dashboard, go to Settings > API
2. Copy your Project URL and anon/public key
3. Update your `.env` file:

```env
EXPO_PUBLIC_SUPABASE_URL=your_project_url_here
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
```

### Step 3: Enable Authentication

1. Go to Authentication > Settings in your Supabase dashboard
2. Configure the following settings:
   - Enable email confirmations (optional for development)
   - Set up email templates if desired
   - Configure redirect URLs for your app

### Step 4: Set up Storage (Optional)

If you want to enable profile photos or other file uploads:

1. Go to Storage in your Supabase dashboard
2. Create a new bucket called `avatars`
3. Set the bucket to public if you want public avatar access
4. Configure RLS policies for the storage bucket

## Database Schema Overview

### Tables Created

1. **users** - Extended user profiles with game data
   - Coins, spins, gems, XP, level
   - Daily streak tracking
   - Profile information

2. **game_sessions** - Track all slot machine spins
   - Bet amounts, payouts, results
   - Game type tracking
   - Historical data for analytics

3. **quests** - Daily/weekly challenges
   - Progress tracking
   - Reward management
   - Expiration handling

4. **purchases** - In-app purchase tracking
   - Transaction validation
   - Reward distribution
   - Purchase history

5. **leaderboards** - Competitive features
   - Weekly/monthly rankings
   - Score tracking
   - Period management

### Key Features

- **Row Level Security (RLS)** - Users can only access their own data
- **Automatic level calculation** - XP automatically updates user level
- **Daily bonus system** - Built-in streak tracking and bonus calculation
- **Optimized indexes** - Fast queries for game operations
- **Data validation** - Constraints prevent invalid data

### Functions Available

- `calculate_level(xp_amount)` - Calculate level from XP
- `xp_for_level(target_level)` - Get XP required for level
- `claim_daily_bonus(user_uuid)` - Handle daily bonus claims

## Testing the Setup

1. Start your Expo development server: `npm start`
2. Try signing up with a test email
3. Check your Supabase dashboard to see if the user was created
4. Verify that the user profile was created with welcome bonus

## Security Notes

- All tables have RLS enabled
- Users can only access their own data
- Database functions use SECURITY DEFINER for controlled access
- Input validation prevents SQL injection and invalid data

## Troubleshooting

### Common Issues

1. **Authentication not working**
   - Check your environment variables
   - Verify Supabase URL and keys are correct
   - Ensure app.config.js is properly configured

2. **Database errors**
   - Check if schema was applied correctly
   - Verify RLS policies are enabled
   - Check Supabase logs for detailed errors

3. **User profile not created**
   - Check if the trigger in useAuth.ts is working
   - Verify the users table exists
   - Check for any constraint violations

### Getting Help

- Check Supabase documentation: [supabase.com/docs](https://supabase.com/docs)
- Review the SQL logs in your Supabase dashboard
- Test queries directly in the SQL Editor
