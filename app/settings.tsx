import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  SafeAreaView,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useAuthContext } from '../src/contexts/AuthContext';
import { LoadingScreen } from '../src/components/common/LoadingScreen';
import { supabase } from '../src/utils/supabase';
import { Colors, Shadows } from '../src/constants/colors';

interface SettingsState {
  soundEnabled: boolean;
  musicEnabled: boolean;
  notificationsEnabled: boolean;
  vibrationEnabled: boolean;
  autoSpinEnabled: boolean;
  darkMode: boolean;
  loading: boolean;
}

export default function SettingsScreen() {
  const { user, signOut } = useAuthContext();
  const router = useRouter();
  
  const [settings, setSettings] = useState<SettingsState>({
    soundEnabled: true,
    musicEnabled: true,
    notificationsEnabled: true,
    vibrationEnabled: true,
    autoSpinEnabled: false,
    darkMode: false,
    loading: true,
  });

  // Load settings from AsyncStorage
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const savedSettings = await AsyncStorage.getItem('app_settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed, loading: false }));
      } else {
        setSettings(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      setSettings(prev => ({ ...prev, loading: false }));
    }
  };

  const saveSettings = async (newSettings: Partial<SettingsState>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      setSettings(updatedSettings);
      
      // Save to AsyncStorage (exclude loading state)
      const { loading, ...settingsToSave } = updatedSettings;
      await AsyncStorage.setItem('app_settings', JSON.stringify(settingsToSave));
    } catch (error) {
      console.error('Error saving settings:', error);
      Alert.alert('Error', 'Failed to save settings');
    }
  };

  const handleToggle = (key: keyof SettingsState) => {
    const newValue = !settings[key];
    saveSettings({ [key]: newValue });
  };

  const handleSignOut = async () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await signOut();
              router.replace('/auth');
            } catch (error) {
              console.error('Sign out error:', error);
              Alert.alert('Error', 'Failed to sign out');
            }
          },
        },
      ]
    );
  };

  const handleResetData = async () => {
    Alert.alert(
      'Reset All Data',
      'This will delete all your progress, coins, and achievements. This action cannot be undone!',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              if (!user) return;
              
              // Delete user data from Supabase
              await supabase.from('users').delete().eq('id', user.id);
              await supabase.from('quests').delete().eq('user_id', user.id);
              await supabase.from('game_sessions').delete().eq('user_id', user.id);
              await supabase.from('purchases').delete().eq('user_id', user.id);
              await supabase.from('leaderboards').delete().eq('user_id', user.id);
              
              // Clear AsyncStorage
              await AsyncStorage.clear();
              
              // Sign out
              await signOut();
              router.replace('/auth');
              
              Alert.alert('Success', 'All data has been reset');
            } catch (error) {
              console.error('Error resetting data:', error);
              Alert.alert('Error', 'Failed to reset data');
            }
          },
        },
      ]
    );
  };

  const handleRestorePurchases = async () => {
    Alert.alert(
      'Restore Purchases',
      'This will restore any previous purchases made with this Apple ID or Google account.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Restore',
          onPress: async () => {
            try {
              // In a real app, this would call RevenueCat's restore function
              // For now, we'll just show a success message
              Alert.alert('Success', 'Purchase restoration completed');
            } catch (error) {
              console.error('Error restoring purchases:', error);
              Alert.alert('Error', 'Failed to restore purchases');
            }
          },
        },
      ]
    );
  };

  if (settings.loading) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      
      <View style={styles.header}>
        <Text style={styles.title}>⚙️ Settings</Text>
        <Text style={styles.subtitle}>Customize your gaming experience</Text>
      </View>
      
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Audio Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔊 Audio</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Sound Effects</Text>
              <Text style={styles.settingDescription}>
                Play sound effects for spins, wins, and interactions
              </Text>
            </View>
            <Switch
              value={settings.soundEnabled}
              onValueChange={() => handleToggle('soundEnabled')}
              trackColor={{ false: Colors.disabled, true: Colors.primary }}
              thumbColor={Colors.text}
            />
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Background Music</Text>
              <Text style={styles.settingDescription}>
                Play background music while gaming
              </Text>
            </View>
            <Switch
              value={settings.musicEnabled}
              onValueChange={() => handleToggle('musicEnabled')}
              trackColor={{ false: Colors.disabled, true: Colors.primary }}
              thumbColor={Colors.text}
            />
          </View>
        </View>

        {/* Notifications */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔔 Notifications</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Push Notifications</Text>
              <Text style={styles.settingDescription}>
                Receive notifications for daily bonuses and special offers
              </Text>
            </View>
            <Switch
              value={settings.notificationsEnabled}
              onValueChange={() => handleToggle('notificationsEnabled')}
              trackColor={{ false: Colors.disabled, true: Colors.primary }}
              thumbColor={Colors.text}
            />
          </View>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Vibration</Text>
              <Text style={styles.settingDescription}>
                Vibrate on wins and special events
              </Text>
            </View>
            <Switch
              value={settings.vibrationEnabled}
              onValueChange={() => handleToggle('vibrationEnabled')}
              trackColor={{ false: Colors.disabled, true: Colors.primary }}
              thumbColor={Colors.text}
            />
          </View>
        </View>

        {/* Gameplay */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎮 Gameplay</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Auto-Spin Default</Text>
              <Text style={styles.settingDescription}>
                Enable auto-spin by default in slot games
              </Text>
            </View>
            <Switch
              value={settings.autoSpinEnabled}
              onValueChange={() => handleToggle('autoSpinEnabled')}
              trackColor={{ false: Colors.disabled, true: Colors.primary }}
              thumbColor={Colors.text}
            />
          </View>
        </View>

        {/* Account Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>👤 Account</Text>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleRestorePurchases}>
            <Text style={styles.actionButtonText}>Restore Purchases</Text>
            <Text style={styles.actionButtonSubtext}>
              Restore any previous in-app purchases
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.actionButton} onPress={handleSignOut}>
            <Text style={styles.actionButtonText}>Sign Out</Text>
            <Text style={styles.actionButtonSubtext}>
              Sign out of your account
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.actionButton, styles.dangerButton]} 
            onPress={handleResetData}
          >
            <Text style={[styles.actionButtonText, styles.dangerText]}>
              Reset All Data
            </Text>
            <Text style={[styles.actionButtonSubtext, styles.dangerText]}>
              Delete all progress and start over
            </Text>
          </TouchableOpacity>
        </View>

        {/* App Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>ℹ️ About</Text>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Version</Text>
            <Text style={styles.infoValue}>1.0.0</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>User ID</Text>
            <Text style={styles.infoValue}>{user?.id?.slice(0, 8)}...</Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.card,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    ...Shadows.card,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 18,
  },
  actionButton: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    ...Shadows.card,
  },
  dangerButton: {
    borderWidth: 1,
    borderColor: Colors.error,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  actionButtonSubtext: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  dangerText: {
    color: Colors.error,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  infoLabel: {
    fontSize: 16,
    color: Colors.text,
  },
  infoValue: {
    fontSize: 16,
    color: Colors.textSecondary,
    fontFamily: 'monospace',
  },
});
