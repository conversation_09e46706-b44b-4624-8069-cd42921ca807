import React, { useEffect } from 'react';
import { Stack } from 'expo-router';
import { Provider } from 'react-redux';
import { store } from '../src/store';
import { Colors } from '../src/constants/colors';
import * as SplashScreen from 'expo-splash-screen';
import { AuthProvider } from '../src/contexts/AuthContext';

// Keep the splash screen visible while we fetch resources
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  useEffect(() => {
    // Hide splash screen after app is mounted
    SplashScreen.hideAsync();
  }, []);

  return (
    <Provider store={store}>
      <AuthProvider>
        <Stack
          screenOptions={{
            headerStyle: {
              backgroundColor: Colors.background,
            },
            headerTintColor: Colors.primary,
            headerTitleStyle: {
              fontWeight: 'bold',
            },
            contentStyle: {
              backgroundColor: Colors.background,
            },
          }}
        >
          <Stack.Screen
            name="index"
            options={{
              headerShown: false
            }}
          />
          <Stack.Screen
            name="auth"
            options={{
              headerShown: false,
              presentation: 'fullScreenModal',
            }}
          />
          <Stack.Screen
            name="lobby"
            options={{
              headerShown: false
            }}
          />
          <Stack.Screen
            name="slot/[gameId]"
            options={{
              title: 'Slot Machine',
              presentation: 'fullScreenModal',
            }}
          />
          <Stack.Screen
            name="daily-bonus"
            options={{
              title: 'Daily Bonus',
              presentation: 'modal',
            }}
          />
          <Stack.Screen
            name="shop"
            options={{
              title: 'Shop',
              presentation: 'modal',
            }}
          />
          <Stack.Screen
            name="profile"
            options={{
              title: 'Profile',
              presentation: 'modal',
            }}
          />
          <Stack.Screen
            name="settings"
            options={{
              title: 'Settings',
              presentation: 'modal',
            }}
          />
        </Stack>
      </AuthProvider>
    </Provider>
  );
}