import React, { useState } from 'react';
import { Text, View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Link, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAuthContext } from '../src/contexts/AuthContext';
import { ScreenContainer } from '../src/components/common/ScreenContainer';
import { LoadingScreen } from '../src/components/common/LoadingScreen';
import { BalanceDisplay } from '../src/components/common/BalanceDisplay';
import { LevelProgress } from '../src/components/common/LevelProgress';
import { GameTile } from '../src/components/common/GameTile';
import { PurchaseModal } from '../src/components/common/PurchaseModal';
import { NewQuestBanner } from '../src/components/common/NewQuestBanner';
import { TutorialOverlay, defaultTutorialSteps } from '../src/components/popups/TutorialOverlay';
import { Button } from '../src/components/common/Button';
import { SlotMachine } from '../src/components/slot/SlotMachine';
import { SpinButton } from '../src/components/slot/SpinButton';
import { useCoins, useLevel, useSlotEngine } from '../src/hooks';
import { Colors } from '../src/constants/colors';

export default function LobbyScreen() {
  const { user, signOut } = useAuthContext();
  const router = useRouter();
  const [purchaseModalVisible, setPurchaseModalVisible] = useState(false);

  // Use new Supabase-powered hooks
  const { balance: coins, loading: coinsLoading, canAfford } = useCoins();
  const {
    level,
    currentXP,
    nextLevelXP,
    progressToNext,
    loading: levelLoading
  } = useLevel();
  const { spin, spinning, lastResult } = useSlotEngine();

  const handleSignOut = async () => {
    try {
      await signOut();
      router.replace('/auth');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  const handleQuickSpin = async () => {
    const betAmount = 10; // Minimum bet
    if (!canAfford(betAmount)) {
      setPurchaseModalVisible(true);
      return;
    }

    try {
      await spin(betAmount);
    } catch (error) {
      console.error('Spin error:', error);
    }
  };

  if (coinsLoading || levelLoading) {
    return <LoadingScreen />;
  }

  return (
    <ScreenContainer>
      <StatusBar style="light" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>🎰 JACKPOT PARTY</Text>
        <Text style={styles.welcomeText}>Welcome, {user?.user_metadata?.username || user?.email}!</Text>
        
        <TouchableOpacity style={styles.signOutButton} onPress={handleSignOut}>
          <Text style={styles.signOutText}>Sign Out</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView contentContainerStyle={styles.content}>
        {/* Balance Display */}
        <BalanceDisplay coins={coins} spins={0} gems={0} />

        {/* Level Progress */}
        <LevelProgress
          level={level}
          currentXP={currentXP}
          nextLevelXP={nextLevelXP}
          progressToNext={progressToNext}
          loading={levelLoading}
        />

        {/* New Quest Banner */}
        <NewQuestBanner
          quest={{
            id: 'daily_spins',
            title: 'Daily Spinner',
            description: 'Spin the slots 10 times today',
            progress: 0.3,
            reward_coins: 100,
            reward_xp: 50,
            completed: false,
          }}
          onPress={() => {/* Handle quest details */}}
        />

        {/* Daily Bonus Banner */}
        <TouchableOpacity
          style={styles.bonusBanner}
          onPress={() => router.push('/daily-bonus')}
        >
          <Text style={styles.bonusBannerText}>🎁 Daily Bonus Available!</Text>
          <Text style={styles.bonusBannerSubtext}>Tap to claim your reward</Text>
        </TouchableOpacity>

        {/* Quick Play Slot Machine */}
        <View style={styles.quickPlaySection}>
          <Text style={styles.sectionTitle}>Quick Play</Text>
          {lastResult && (
            <SlotMachine reels={lastResult.matrix} />
          )}
          <SpinButton
            onPress={handleQuickSpin}
            disabled={spinning || !canAfford(10)}
            spinning={spinning}
          />
          {lastResult && lastResult.win > 0 && (
            <Text style={styles.winText}>
              🎉 You won {lastResult.win} coins! 🎉
            </Text>
          )}
        </View>
        
        {/* Game Selection */}
        <View style={styles.gamesSection}>
          <Text style={styles.sectionTitle}>Games</Text>
          <View style={styles.gameGrid}>
            <GameTile
              title="Classic Slots"
              subtitle="Traditional 3-reel slots"
              emoji="🎰"
              onPress={() => router.push('/slot/classic')}
            />

            <GameTile
              title="Mega Slots"
              subtitle="5-reel with bonus features"
              emoji="💎"
              onPress={() => router.push('/slot/mega')}
              isNew={true}
            />

            <GameTile
              title="Progressive"
              subtitle="Massive jackpots await"
              emoji="🏆"
              onPress={() => {}}
              locked={true}
            />

            <GameTile
              title="Bonus Rounds"
              subtitle="Special mini-games"
              emoji="🎯"
              onPress={() => {}}
              locked={true}
            />
          </View>
        </View>
        
        {/* Navigation */}
        <View style={styles.navSection}>
          <View style={styles.navRow}>
            <Button
              title="🛒 Buy Coins"
              onPress={() => setPurchaseModalVisible(true)}
              variant="primary"
              style={styles.navButton}
            />
            <Link href="/daily-bonus" asChild>
              <Button title="🎁 Daily Bonus" onPress={() => {}} variant="success" style={styles.navButton} />
            </Link>
          </View>

          <View style={styles.navRow}>
            <Link href="/profile" asChild>
              <Button title="👤 Profile" onPress={() => {}} variant="secondary" style={styles.navButton} />
            </Link>
            <Link href="/settings" asChild>
              <Button title="⚙️ Settings" onPress={() => {}} variant="secondary" style={styles.navButton} />
            </Link>
          </View>
        </View>
      </ScrollView>

      {/* Purchase Modal */}
      <PurchaseModal
        visible={purchaseModalVisible}
        onClose={() => setPurchaseModalVisible(false)}
      />

      {/* Tutorial Overlay */}
      <TutorialOverlay
        steps={defaultTutorialSteps}
        onComplete={() => console.log('Tutorial completed')}
        onSkip={() => console.log('Tutorial skipped')}
      />
    </ScreenContainer>
  );
}

const styles = StyleSheet.create({
  header: {
    alignItems: 'center',
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.card,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 4,
  },
  welcomeText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginBottom: 12,
  },
  signOutButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: Colors.error,
    borderRadius: 8,
  },
  signOutText: {
    color: Colors.text,
    fontSize: 14,
    fontWeight: '600',
  },
  content: {
    padding: 16,
  },
  bonusBanner: {
    backgroundColor: Colors.success,
    borderRadius: 12,
    padding: 16,
    marginBottom: 20,
    alignItems: 'center',
  },
  bonusBannerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  bonusBannerSubtext: {
    fontSize: 14,
    color: Colors.text,
    opacity: 0.8,
  },
  quickPlaySection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  gamesSection: {
    marginBottom: 24,
  },
  gameGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  winText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
    textAlign: 'center',
    marginTop: 12,
  },
  navSection: {
    marginBottom: 24,
  },
  navRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  navButton: {
    flex: 1,
    marginHorizontal: 6,
  },
});
