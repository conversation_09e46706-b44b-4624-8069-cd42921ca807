import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, Alert, SafeAreaView } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming, 
  withSequence,
  runOnJS,
} from 'react-native-reanimated';
import { useAuthContext } from '../../src/contexts/AuthContext';
import { LoadingScreen } from '../../src/components/common/LoadingScreen';
import { BalanceDisplay } from '../../src/components/common/BalanceDisplay';
import { AnimatedReel } from '../../src/components/slot/AnimatedReel';
import { SpinButton } from '../../src/components/slot/SpinButton';
import { BetControl } from '../../src/components/slot/BetControl';
import { AutoSpinToggle } from '../../src/components/slot/AutoSpinToggle';
import { WinDisplay } from '../../src/components/slot/WinDisplay';
import { useCoins, useLevel, useSlotEngine } from '../../src/hooks';
import { Colors } from '../../src/constants/colors';

export default function SlotScreen() {
  const { gameId } = useLocalSearchParams<{ gameId: string }>();
  const router = useRouter();
  const { user } = useAuthContext();
  
  const [betAmount, setBetAmount] = useState(10);
  const [autoSpin, setAutoSpin] = useState(false);
  const [autoSpinCount, setAutoSpinCount] = useState(0);
  const [maxAutoSpins, setMaxAutoSpins] = useState(10);
  
  const { balance: coins, loading: coinsLoading, subtract: subtractCoins, add: addCoins } = useCoins();
  const { addXP } = useLevel();
  const { spin, spinning, lastResult, getAllSymbols } = useSlotEngine();
  
  // Animation values
  const winAmountScale = useSharedValue(0);
  const winAmountOpacity = useSharedValue(0);
  
  const symbols = getAllSymbols();
  const gameTitle = gameId === 'classic' ? 'Classic Slots' : 
                   gameId === 'mega' ? 'Mega Slots' : 'Slot Machine';

  const handleSpin = async () => {
    if (coins < betAmount) {
      Alert.alert('Insufficient Coins', 'You need more coins to place this bet. Visit the shop to buy more!');
      return;
    }

    try {
      // Subtract bet amount first
      const success = await subtractCoins(betAmount);
      if (!success) {
        Alert.alert('Error', 'Failed to place bet. Please try again.');
        return;
      }

      // Perform spin
      const result = await spin(betAmount);
      
      // Handle win
      if (result.win > 0) {
        await addCoins(result.win);
        await addXP(Math.floor(result.win / 10)); // 1 XP per 10 coins won
        
        // Animate win display
        animateWin();
        
        if (result.isJackpot) {
          Alert.alert('🎉 JACKPOT! 🎉', `Incredible! You won ${result.win} coins!`);
        }
      }
      
      // Handle auto-spin
      if (autoSpin && autoSpinCount > 0) {
        setAutoSpinCount(prev => prev - 1);
        if (autoSpinCount <= 1) {
          setAutoSpin(false);
          setAutoSpinCount(0);
        }
      }
      
    } catch (error) {
      console.error('Spin error:', error);
      Alert.alert('Error', 'Spin failed. Please try again.');
    }
  };

  const animateWin = () => {
    winAmountScale.value = withSequence(
      withTiming(1.2, { duration: 300 }),
      withTiming(1, { duration: 200 })
    );
    winAmountOpacity.value = withSequence(
      withTiming(1, { duration: 300 }),
      withTiming(0.8, { duration: 2000 }),
      withTiming(0, { duration: 500 })
    );
  };

  const handleAutoSpin = (enabled: boolean, count: number) => {
    setAutoSpin(enabled);
    setMaxAutoSpins(count);
    setAutoSpinCount(count);
  };

  // Auto-spin effect
  useEffect(() => {
    if (autoSpin && autoSpinCount > 0 && !spinning) {
      const timer = setTimeout(() => {
        handleSpin();
      }, 1000); // 1 second delay between auto spins
      
      return () => clearTimeout(timer);
    }
  }, [autoSpin, autoSpinCount, spinning]);

  const winAmountAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: winAmountScale.value }],
      opacity: winAmountOpacity.value,
    };
  });

  if (coinsLoading) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>{gameTitle}</Text>
        <BalanceDisplay coins={coins} spins={0} gems={0} />
      </View>
      
      {/* Slot Machine */}
      <View style={styles.slotContainer}>
        <View style={styles.reelsContainer}>
          {lastResult ? (
            lastResult.matrix.map((reel, index) => (
              <AnimatedReel
                key={index}
                symbols={reel}
                spinning={spinning}
                delay={index * 100}
                winningLines={lastResult.winningLines}
                reelIndex={index}
              />
            ))
          ) : (
            // Default reels
            Array.from({ length: 5 }, (_, index) => (
              <AnimatedReel
                key={index}
                symbols={['🍒', '🍋', '🔔']}
                spinning={spinning}
                delay={index * 100}
                winningLines={[]}
                reelIndex={index}
              />
            ))
          )}
        </View>
        
        {/* Win Display */}
        {lastResult && lastResult.win > 0 && (
          <Animated.View style={[styles.winContainer, winAmountAnimatedStyle]}>
            <WinDisplay 
              amount={lastResult.win} 
              isJackpot={lastResult.isJackpot}
            />
          </Animated.View>
        )}
      </View>
      
      {/* Controls */}
      <View style={styles.controlsContainer}>
        <BetControl
          betAmount={betAmount}
          onBetChange={setBetAmount}
          maxBet={Math.min(coins, 100)}
          disabled={spinning || autoSpin}
        />
        
        <SpinButton
          onPress={handleSpin}
          disabled={spinning || coins < betAmount}
          spinning={spinning}
          betAmount={betAmount}
        />
        
        <AutoSpinToggle
          enabled={autoSpin}
          count={autoSpinCount}
          maxCount={maxAutoSpins}
          onToggle={handleAutoSpin}
          disabled={spinning || coins < betAmount}
        />
      </View>
      
      {/* Game Info */}
      <View style={styles.infoContainer}>
        <Text style={styles.infoText}>
          Min Bet: 10 • Max Bet: 100 • RTP: ~95%
        </Text>
        {autoSpin && (
          <Text style={styles.autoSpinText}>
            Auto Spins Remaining: {autoSpinCount}
          </Text>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.card,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 12,
  },
  slotContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  reelsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
  },
  winContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -100 }, { translateY: -50 }],
    zIndex: 10,
  },
  controlsContainer: {
    padding: 20,
    gap: 16,
  },
  infoContainer: {
    padding: 20,
    alignItems: 'center',
  },
  infoText: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 8,
  },
  autoSpinText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: 'bold',
  },
});
