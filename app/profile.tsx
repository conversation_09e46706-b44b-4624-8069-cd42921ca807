import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { ScreenContainer } from '../src/components/common/ScreenContainer';
import { Button } from '../src/components/common/Button';
import { useAppSelector } from '../src/store/hooks';
import { Colors } from '../src/constants/colors';

export default function ProfileScreen() {
  const { deviceId, profile } = useAppSelector((state) => state.user);

  return (
    <ScreenContainer>
      <Text style={styles.title}>Profile</Text>
      
      <View style={styles.infoContainer}>
        <Text style={styles.label}>Device ID</Text>
        <Text style={styles.value}>{deviceId || 'Loading...'}</Text>
      </View>
      
      {profile && (
        <>
          <View style={styles.infoContainer}>
            <Text style={styles.label}>Display Name</Text>
            <Text style={styles.value}>{profile.displayName}</Text>
          </View>
          
          <View style={styles.infoContainer}>
            <Text style={styles.label}>Level</Text>
            <Text style={styles.value}>{profile.level}</Text>
          </View>
          
          <View style={styles.infoContainer}>
            <Text style={styles.label}>XP</Text>
            <Text style={styles.value}>{profile.xp}</Text>
          </View>
        </>
      )}
      
      <Text style={styles.comingSoon}>
        More features coming soon!{'\n'}
        • Achievements{'\n'}
        • Statistics{'\n'}
        • Customization
      </Text>
      
      <Button 
        title="Back to Game" 
        onPress={() => router.back()} 
        style={styles.backButton}
      />
    </ScreenContainer>
  );
}

const styles = StyleSheet.create({
  title: {
    color: Colors.primary,
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 32,
  },
  infoContainer: {
    backgroundColor: Colors.card,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    width: '80%',
  },
  label: {
    color: Colors.textSecondary,
    fontSize: 14,
    marginBottom: 4,
  },
  value: {
    color: Colors.text,
    fontSize: 18,
    fontWeight: 'bold',
  },
  comingSoon: {
    color: Colors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    marginTop: 32,
    lineHeight: 24,
  },
  backButton: {
    marginTop: 32,
  },
});