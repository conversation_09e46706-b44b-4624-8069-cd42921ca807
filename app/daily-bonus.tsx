import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { useAuthContext } from '../src/contexts/AuthContext';
import { LoadingScreen } from '../src/components/common/LoadingScreen';
import { WheelOfFortune } from '../src/components/bonus/WheelOfFortune';
import { StreakReward } from '../src/components/bonus/StreakReward';
import { supabase } from '../src/utils/supabase';
import { useCoins, useLevel } from '../src/hooks';
import { Colors, Shadows } from '../src/constants/colors';

interface DailyBonusData {
  available: boolean;
  streak: number;
  lastClaimed: string | null;
  nextBonusAt: string;
}

export default function DailyBonusScreen() {
  const { user } = useAuthContext();
  const router = useRouter();
  const { add: addCoins } = useCoins();
  const { addXP } = useLevel();
  
  const [bonusData, setBonusData] = useState<DailyBonusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [claiming, setClaiming] = useState(false);
  const [wheelSpinning, setWheelSpinning] = useState(false);
  const [lastWinAmount, setLastWinAmount] = useState<number | null>(null);

  useEffect(() => {
    loadBonusData();
  }, []);

  const loadBonusData = async () => {
    if (!user) return;

    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('users')
        .select('daily_streak, last_daily_bonus')
        .eq('id', user.id)
        .single();

      if (error) {
        throw error;
      }

      const lastBonus = data?.last_daily_bonus ? new Date(data.last_daily_bonus) : null;
      const today = new Date();
      const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      const lastBonusDate = lastBonus ? new Date(lastBonus.getFullYear(), lastBonus.getMonth(), lastBonus.getDate()) : null;
      
      const available = !lastBonusDate || lastBonusDate < todayStart;
      const nextBonusAt = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000).toISOString();

      setBonusData({
        available,
        streak: data?.daily_streak || 0,
        lastClaimed: data?.last_daily_bonus || null,
        nextBonusAt,
      });

    } catch (error) {
      console.error('Error loading bonus data:', error);
      Alert.alert('Error', 'Failed to load bonus data');
    } finally {
      setLoading(false);
    }
  };

  const handleClaimBonus = async () => {
    if (!user || !bonusData?.available || claiming) return;

    try {
      setClaiming(true);
      setWheelSpinning(true);

      // Call the database function to claim daily bonus
      const { data, error } = await supabase.rpc('claim_daily_bonus', {
        user_uuid: user.id
      });

      if (error) {
        throw error;
      }

      const result = data[0];
      const bonusAmount = result.bonus_amount;
      const newStreak = result.new_streak;

      // Update local state
      await addCoins(bonusAmount);
      await addXP(bonusAmount / 2); // Half the coins as XP

      setLastWinAmount(bonusAmount);
      
      // Update bonus data
      setBonusData(prev => prev ? {
        ...prev,
        available: false,
        streak: newStreak,
        lastClaimed: new Date().toISOString(),
      } : null);

      // Show success message after wheel animation
      setTimeout(() => {
        Alert.alert(
          '🎉 Daily Bonus Claimed!',
          `You received ${bonusAmount} coins!\nDaily streak: ${newStreak} days`,
          [
            {
              text: 'Awesome!',
              onPress: () => router.back(),
            }
          ]
        );
      }, 3000);

    } catch (error) {
      console.error('Error claiming bonus:', error);
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to claim bonus');
      setWheelSpinning(false);
    } finally {
      setClaiming(false);
    }
  };

  const getTimeUntilNextBonus = (): string => {
    if (!bonusData?.nextBonusAt) return '';
    
    const now = new Date();
    const nextBonus = new Date(bonusData.nextBonusAt);
    const diff = nextBonus.getTime() - now.getTime();
    
    if (diff <= 0) return 'Available now!';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  if (loading) {
    return <LoadingScreen />;
  }

  if (!bonusData) {
    return (
      <SafeAreaView style={styles.container}>
        <Text style={styles.errorText}>Failed to load bonus data</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      
      <View style={styles.header}>
        <Text style={styles.title}>🎁 Daily Bonus</Text>
        <Text style={styles.subtitle}>
          Spin the wheel for your daily reward!
        </Text>
      </View>

      <View style={styles.content}>
        {/* Streak Display */}
        <StreakReward 
          streak={bonusData.streak}
          nextStreakBonus={bonusData.streak + 1}
        />

        {/* Wheel of Fortune */}
        <View style={styles.wheelContainer}>
          <WheelOfFortune
            spinning={wheelSpinning}
            onSpinComplete={() => setWheelSpinning(false)}
            winAmount={lastWinAmount}
          />
        </View>

        {/* Claim Button or Status */}
        <View style={styles.actionContainer}>
          {bonusData.available ? (
            <TouchableOpacity
              style={[styles.claimButton, claiming && styles.claimButtonDisabled]}
              onPress={handleClaimBonus}
              disabled={claiming}
            >
              <Text style={styles.claimButtonText}>
                {claiming ? 'Spinning...' : 'Claim Daily Bonus'}
              </Text>
            </TouchableOpacity>
          ) : (
            <View style={styles.unavailableContainer}>
              <Text style={styles.unavailableText}>
                ✅ Daily bonus already claimed!
              </Text>
              <Text style={styles.nextBonusText}>
                Next bonus in: {getTimeUntilNextBonus()}
              </Text>
            </View>
          )}
        </View>

        {/* Bonus Info */}
        <View style={styles.infoContainer}>
          <Text style={styles.infoTitle}>Daily Bonus Rules</Text>
          <Text style={styles.infoText}>
            • Base reward: 50 coins + 25 XP
          </Text>
          <Text style={styles.infoText}>
            • Streak bonus: +10 coins per consecutive day
          </Text>
          <Text style={styles.infoText}>
            • Maximum bonus: 200 coins per day
          </Text>
          <Text style={styles.infoText}>
            • Reset time: Midnight local time
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.card,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  wheelContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
  },
  actionContainer: {
    marginVertical: 20,
  },
  claimButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    ...Shadows.button,
  },
  claimButtonDisabled: {
    opacity: 0.6,
  },
  claimButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.background,
  },
  unavailableContainer: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  unavailableText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.success,
    marginBottom: 8,
  },
  nextBonusText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  infoContainer: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginTop: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
    lineHeight: 20,
  },
  errorText: {
    fontSize: 16,
    color: Colors.error,
    textAlign: 'center',
    margin: 20,
  },
});
