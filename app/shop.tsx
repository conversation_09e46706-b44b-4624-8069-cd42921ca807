import React from 'react';
import { Text, View, StyleSheet, ScrollView } from 'react-native';
import { router } from 'expo-router';
import { ScreenContainer } from '../src/components/common/ScreenContainer';
import { Button } from '../src/components/common/Button';
import { Colors } from '../src/constants/colors';

export default function ShopScreen() {
  return (
    <ScreenContainer style={styles.container}>
      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.title}>Shop</Text>
        <Text style={styles.subtitle}>Coming soon!</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🪙 Coins</Text>
          <Text style={styles.description}>
            Purchase coin bundles to keep spinning
          </Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎰 Spins</Text>
          <Text style={styles.description}>
            Buy spin packages for endless fun
          </Text>
        </View>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💎 Special Offers</Text>
          <Text style={styles.description}>
            Limited time deals and multipliers
          </Text>
        </View>
        
        <Button 
          title="Back to Game" 
          onPress={() => router.back()} 
          style={styles.backButton}
        />
      </ScrollView>
    </ScreenContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'stretch',
    justifyContent: 'flex-start',
  },
  content: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    color: Colors.primary,
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  subtitle: {
    color: Colors.text,
    fontSize: 20,
    marginBottom: 32,
  },
  section: {
    width: '100%',
    backgroundColor: Colors.card,
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
  },
  sectionTitle: {
    color: Colors.primary,
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    color: Colors.textSecondary,
    fontSize: 16,
  },
  backButton: {
    marginTop: 32,
  },
});