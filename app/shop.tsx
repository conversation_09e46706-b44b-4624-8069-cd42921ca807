import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useAuthContext } from '../src/contexts/AuthContext';
import { LoadingScreen } from '../src/components/common/LoadingScreen';
import { BalanceDisplay } from '../src/components/common/BalanceDisplay';
import { PurchaseModal } from '../src/components/common/PurchaseModal';
import { useCoins, usePurchases } from '../src/hooks';
import { Colors, Shadows } from '../src/constants/colors';

interface ShopItemProps {
  title: string;
  description: string;
  price: string;
  coins: number;
  gems?: number;
  spins?: number;
  onPress: () => void;
  popular?: boolean;
  bestValue?: boolean;
  sale?: boolean;
}

const ShopItem: React.FC<ShopItemProps> = ({
  title,
  description,
  price,
  coins,
  gems = 0,
  spins = 0,
  onPress,
  popular = false,
  bestValue = false,
  sale = false,
}) => {
  return (
    <TouchableOpacity style={[
      styles.shopItem,
      popular && styles.popularItem,
      bestValue && styles.bestValueItem,
    ]} onPress={onPress}>
      {popular && (
        <View style={styles.badge}>
          <Text style={styles.badgeText}>POPULAR</Text>
        </View>
      )}

      {bestValue && (
        <View style={[styles.badge, styles.bestValueBadge]}>
          <Text style={styles.badgeText}>BEST VALUE</Text>
        </View>
      )}

      {sale && (
        <View style={[styles.badge, styles.saleBadge]}>
          <Text style={styles.badgeText}>SALE</Text>
        </View>
      )}

      <View style={styles.itemHeader}>
        <Text style={styles.itemTitle}>{title}</Text>
        <Text style={styles.itemPrice}>{price}</Text>
      </View>

      <Text style={styles.itemDescription}>{description}</Text>

      <View style={styles.rewardsContainer}>
        <View style={styles.rewardItem}>
          <Text style={styles.rewardEmoji}>🪙</Text>
          <Text style={styles.rewardText}>{coins.toLocaleString()}</Text>
        </View>

        {gems > 0 && (
          <View style={styles.rewardItem}>
            <Text style={styles.rewardEmoji}>💎</Text>
            <Text style={styles.rewardText}>{gems}</Text>
          </View>
        )}

        {spins > 0 && (
          <View style={styles.rewardItem}>
            <Text style={styles.rewardEmoji}>🎰</Text>
            <Text style={styles.rewardText}>{spins}</Text>
          </View>
        )}
      </View>

      <View style={styles.buyButton}>
        <Text style={styles.buyButtonText}>Buy Now</Text>
      </View>
    </TouchableOpacity>
  );
};

export default function ShopScreen() {
  const { user } = useAuthContext();
  const { balance: coins, loading: coinsLoading } = useCoins();
  const { offerings, loading: purchasesLoading, purchase } = usePurchases();
  const [purchaseModalVisible, setPurchaseModalVisible] = useState(false);

  const handlePurchase = async (packageId: string) => {
    const success = await purchase(packageId);
    if (success) {
      // Purchase successful, modal will show success message
    }
  };

  if (coinsLoading || purchasesLoading) {
    return <LoadingScreen />;
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />

      <View style={styles.header}>
        <Text style={styles.title}>🛒 Coin Shop</Text>
        <Text style={styles.subtitle}>
          Get more coins to keep the fun going!
        </Text>
        <BalanceDisplay coins={coins} spins={0} gems={0} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Featured Offers */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>💰 Coin Packages</Text>
          <Text style={styles.sectionSubtitle}>
            Choose the perfect package for your gaming style
          </Text>

          <View style={styles.itemsContainer}>
            <ShopItem
              title="Starter Pack"
              description="Perfect for new players"
              price="$0.99"
              coins={1000}
              spins={5}
              onPress={() => handlePurchase('small_coin_pack')}
            />

            <ShopItem
              title="Value Pack"
              description="Great value for regular players"
              price="$4.99"
              coins={6000}
              gems={10}
              spins={25}
              onPress={() => handlePurchase('medium_coin_pack')}
              popular={true}
            />

            <ShopItem
              title="Mega Pack"
              description="Maximum coins for serious players"
              price="$9.99"
              coins={15000}
              gems={25}
              spins={50}
              onPress={() => handlePurchase('large_coin_pack')}
              bestValue={true}
            />

            <ShopItem
              title="Premium Pack"
              description="Ultimate package with exclusive bonuses"
              price="$19.99"
              coins={35000}
              gems={75}
              spins={100}
              onPress={() => handlePurchase('premium_coin_pack')}
              sale={true}
            />
          </View>
        </View>

        {/* Special Offers */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎁 Special Offers</Text>

          <View style={styles.specialOffer}>
            <Text style={styles.specialOfferTitle}>🔥 Limited Time Offer</Text>
            <Text style={styles.specialOfferDescription}>
              Double coins on your first purchase!
            </Text>
            <TouchableOpacity
              style={styles.specialOfferButton}
              onPress={() => setPurchaseModalVisible(true)}
            >
              <Text style={styles.specialOfferButtonText}>View Offers</Text>
            </TouchableOpacity>
          </View>
        </View>
      </ScrollView>

      {/* Purchase Modal */}
      <PurchaseModal
        visible={purchaseModalVisible}
        onClose={() => setPurchaseModalVisible(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: 20,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.card,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 16,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 16,
  },
  itemsContainer: {
    gap: 16,
  },
  shopItem: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    position: 'relative',
    ...Shadows.card,
  },
  popularItem: {
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  bestValueItem: {
    borderWidth: 2,
    borderColor: Colors.success,
  },
  badge: {
    position: 'absolute',
    top: -1,
    right: 12,
    backgroundColor: Colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    zIndex: 1,
  },
  bestValueBadge: {
    backgroundColor: Colors.success,
  },
  saleBadge: {
    backgroundColor: Colors.error,
  },
  badgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.text,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  itemTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  itemPrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  itemDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 16,
  },
  rewardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  rewardItem: {
    alignItems: 'center',
  },
  rewardEmoji: {
    fontSize: 24,
    marginBottom: 4,
  },
  rewardText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.text,
  },
  buyButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    ...Shadows.button,
  },
  buyButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.background,
  },
  specialOffer: {
    backgroundColor: Colors.success,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  specialOfferTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
  },
  specialOfferDescription: {
    fontSize: 14,
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 16,
  },
  specialOfferButton: {
    backgroundColor: Colors.text,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  specialOfferButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.success,
  },
});