import React from 'react';
import { Text, View, StyleSheet } from 'react-native';
import { Link } from 'expo-router';
import { ScreenContainer } from '../src/components/common/ScreenContainer';
import { LoadingScreen } from '../src/components/common/LoadingScreen';
import { BalanceDisplay } from '../src/components/common/BalanceDisplay';
import { Button } from '../src/components/common/Button';
import { SlotMachine } from '../src/components/slot/SlotMachine';
import { SpinButton } from '../src/components/slot/SpinButton';
import { useDeviceId, useWallet, useGame } from '../src/hooks';
import { Colors } from '../src/constants/colors';

export default function HomeScreen() {
  const { deviceId, loading: deviceLoading } = useDeviceId();
  const { coins, spins, gems, loading: walletLoading } = useWallet(deviceId);
  const {
    reels,
    spinning,
    canSpin,
    dailyBonus,
    handleSpin,
    handleClaimBonus,
  } = useGame(deviceId);

  if (deviceLoading || walletLoading) {
    return <LoadingScreen />;
  }

  return (
    <ScreenContainer>
      <Text style={styles.title}>JACKPOT PARTY</Text>
      
      <BalanceDisplay coins={coins} spins={spins} gems={gems} />
      
      <SlotMachine reels={reels} />
      
      <SpinButton 
        onPress={handleSpin} 
        disabled={!canSpin} 
        spinning={spinning} 
      />
      
      <Button
        title="Claim Daily Bonus"
        onPress={handleClaimBonus}
        disabled={!dailyBonus.available}
        variant="success"
        style={styles.bonusButton}
      />
      
      {dailyBonus.streak > 0 && (
        <Text style={styles.streakText}>Daily Streak: {dailyBonus.streak}</Text>
      )}
      
      <View style={styles.navRow}>
        <Link href="/shop" asChild>
          <Button title="Shop" onPress={() => {}} variant="secondary" />
        </Link>
        <Link href="/profile" asChild>
          <Button title="Profile" onPress={() => {}} variant="secondary" />
        </Link>
      </View>
    </ScreenContainer>
  );
}

const styles = StyleSheet.create({
  title: {
    color: Colors.primary,
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
    letterSpacing: 2,
  },
  bonusButton: {
    marginTop: 20,
  },
  streakText: {
    color: Colors.primary,
    fontSize: 16,
    marginTop: 8,
  },
  navRow: {
    flexDirection: 'row',
    marginTop: 32,
    gap: 16,
  },
});