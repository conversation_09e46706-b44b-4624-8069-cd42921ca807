import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuthContext } from '../src/contexts/AuthContext';
import { LoadingScreen } from '../src/components/common/LoadingScreen';
import { Colors } from '../src/constants/colors';

export default function IndexScreen() {
  const { isAuthenticated, loading, initialized } = useAuthContext();
  const router = useRouter();

  useEffect(() => {
    if (!initialized || loading) {
      return; // Still loading auth state
    }

    if (isAuthenticated) {
      router.replace('/lobby');
    } else {
      router.replace('/auth');
    }
  }, [isAuthenticated, loading, initialized, router]);

  // Show loading screen while determining auth state
  return (
    <View style={styles.container}>
      <LoadingScreen />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
});

const styles = StyleSheet.create({
  title: {
    color: Colors.primary,
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 16,
    letterSpacing: 2,
  },
  bonusButton: {
    marginTop: 20,
  },
  streakText: {
    color: Colors.primary,
    fontSize: 16,
    marginTop: 8,
  },
  navRow: {
    flexDirection: 'row',
    marginTop: 32,
    gap: 16,
  },
});