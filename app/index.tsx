import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuthContext } from '../src/contexts/AuthContext';
import { LoadingScreen } from '../src/components/common/LoadingScreen';
import { PrivacyConsent } from '../src/components/popups/PrivacyConsent';
import { AppTrackingPermission } from '../src/components/popups/AppTrackingPermission';
import { useAnalytics, useNotifications } from '../src/hooks';
import { Colors } from '../src/constants/colors';

export default function IndexScreen() {
  const { isAuthenticated, loading, initialized } = useAuthContext();
  const router = useRouter();
  const { trackAppOpen, trackSessionStart } = useAnalytics();
  const { scheduleDailyBonusReminder } = useNotifications();
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  const [trackingHandled, setTrackingHandled] = useState(false);

  useEffect(() => {
    if (!initialized || loading) {
      return; // Still loading auth state
    }

    // Only proceed to app after privacy consent and tracking permission are handled
    if (privacyAccepted && trackingHandled) {
      if (isAuthenticated) {
        router.replace('/lobby');
      } else {
        router.replace('/auth');
      }
    }
  }, [isAuthenticated, loading, initialized, privacyAccepted, trackingHandled, router]);

  const handlePrivacyAccept = async () => {
    setPrivacyAccepted(true);
    // Track app open and start session
    await trackAppOpen();
    await trackSessionStart();
  };

  const handlePrivacyDecline = () => {
    // In a real app, you might want to close the app or show limited functionality
    setPrivacyAccepted(true);
  };

  const handleTrackingAllow = async () => {
    setTrackingHandled(true);
    console.log('User allowed tracking');
    // Set up notifications after tracking permission
    await scheduleDailyBonusReminder();
  };

  const handleTrackingDeny = async () => {
    setTrackingHandled(true);
    console.log('User denied tracking');
    // Still set up notifications even if tracking is denied
    await scheduleDailyBonusReminder();
  };

  // Show loading screen while determining auth state
  return (
    <View style={styles.container}>
      <LoadingScreen />

      {/* Privacy Consent - shown first */}
      <PrivacyConsent
        onAccept={handlePrivacyAccept}
        onDecline={handlePrivacyDecline}
      />

      {/* App Tracking Permission - shown after privacy consent */}
      {privacyAccepted && (
        <AppTrackingPermission
          onAllow={handleTrackingAllow}
          onDeny={handleTrackingDeny}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
});