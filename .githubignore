# GitHub recommended .gitignore for macOS, Node, Python, VSCode, and common tools
# macOS
.DS_Store
.AppleDouble
.LSOverride

# Node
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log

# Python
__pycache__/
*.py[cod]
*.so
*.egg
*.egg-info/
dist/
build/

# VSCode
.vscode/
.history/

# Logs
*.log

# Environment
.env
.env.*

# Others
.idea/
*.swp
*.swo
*.bak
*.tmp

# System Files
Thumbs.db
Desktop.ini

# Coverage
coverage/
*.cover
*.pyc

# Test
*.test.js
*.test.ts
