# Contributing to Jackpot Party Casino

Thank you for your interest in contributing to Jackpot Party Casino! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Bugs

1. **Check existing issues** to avoid duplicates
2. **Use the bug report template** when creating new issues
3. **Provide detailed information**:
   - Device and OS version
   - App version
   - Steps to reproduce
   - Expected vs actual behavior
   - Screenshots if applicable

### Suggesting Features

1. **Check existing feature requests** to avoid duplicates
2. **Use the feature request template**
3. **Provide clear use cases** and benefits
4. **Consider implementation complexity**

### Code Contributions

#### Getting Started

1. **Fork the repository**
2. **Clone your fork**:
   ```bash
   git clone https://github.com/yourusername/jackpot-party-casino.git
   cd jackpot-party-casino
   ```
3. **Install dependencies**:
   ```bash
   npm install
   ```
4. **Set up environment**:
   ```bash
   cp .env.example .env
   # Add your Supabase credentials
   ```

#### Development Workflow

1. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**:
   - Follow the coding standards below
   - Add tests for new functionality
   - Update documentation if needed

3. **Test your changes**:
   ```bash
   npm test
   npm run type-check
   npm run lint
   ```

4. **Commit your changes**:
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

5. **Push to your fork**:
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a Pull Request**

## 📝 Coding Standards

### TypeScript

- **Use TypeScript** for all new code
- **Define interfaces** for all data structures
- **Use proper typing** - avoid `any` when possible
- **Export types** that might be reused

### React Native

- **Use functional components** with hooks
- **Follow React best practices**:
  - Use `useCallback` and `useMemo` appropriately
  - Avoid unnecessary re-renders
  - Keep components small and focused

### File Structure

```
src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components
│   ├── slot/           # Slot machine specific
│   └── popups/         # Modal and popup components
├── hooks/              # Custom React hooks
├── contexts/           # React contexts
├── utils/              # Utility functions
├── constants/          # App constants
└── test/               # Test utilities
```

### Naming Conventions

- **Components**: PascalCase (`BalanceDisplay`)
- **Hooks**: camelCase starting with `use` (`useCoins`)
- **Files**: Match the main export (`BalanceDisplay.tsx`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_BET_AMOUNT`)

### Code Style

- **Use Prettier** for formatting (configured in project)
- **Use ESLint** for code quality (configured in project)
- **2 spaces** for indentation
- **Single quotes** for strings
- **Trailing commas** in objects and arrays

### Testing

- **Write tests** for new functionality
- **Use React Native Testing Library** for component tests
- **Mock external dependencies** (Supabase, AsyncStorage, etc.)
- **Aim for good coverage** but focus on critical paths

Example test:
```typescript
import { render } from '@testing-library/react-native';
import { BalanceDisplay } from '../BalanceDisplay';

describe('BalanceDisplay', () => {
  it('should display coins correctly', () => {
    const { getByText } = render(
      <BalanceDisplay coins={1000} spins={5} gems={10} />
    );
    expect(getByText('1,000')).toBeTruthy();
  });
});
```

## 🎯 Areas for Contribution

### High Priority
- **Bug fixes** and stability improvements
- **Performance optimizations**
- **Accessibility improvements**
- **Test coverage** expansion

### Medium Priority
- **New slot machine themes**
- **Additional quest types**
- **UI/UX enhancements**
- **Analytics improvements**

### Low Priority
- **Documentation improvements**
- **Code refactoring**
- **Developer experience** improvements

## 🔍 Code Review Process

1. **All PRs require review** before merging
2. **Automated checks** must pass:
   - Tests
   - Type checking
   - Linting
   - Build verification

3. **Review criteria**:
   - Code quality and style
   - Test coverage
   - Performance impact
   - Security considerations
   - Documentation updates

## 🚀 Release Process

1. **Feature branches** merge to `develop`
2. **Release candidates** are created from `develop`
3. **Production releases** merge to `main`
4. **Hotfixes** can be applied directly to `main`

## 📋 Commit Message Format

Use conventional commits:

```
type(scope): description

[optional body]

[optional footer]
```

Types:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test additions or changes
- `chore`: Build process or auxiliary tool changes

Examples:
```
feat(slots): add new Egyptian theme slot machine
fix(auth): resolve sign-in error on iOS
docs(readme): update installation instructions
```

## 🛡️ Security

- **Never commit** sensitive information (API keys, passwords)
- **Use environment variables** for configuration
- **Follow security best practices** for React Native
- **Report security issues** privately to maintainers

## 📞 Getting Help

- **GitHub Discussions** for questions and ideas
- **GitHub Issues** for bugs and feature requests
- **Code review comments** for implementation feedback

## 🙏 Recognition

Contributors will be:
- **Listed in the README** contributors section
- **Mentioned in release notes** for significant contributions
- **Invited to the contributors team** for ongoing contributors

Thank you for helping make Jackpot Party Casino better! 🎰✨
