# 🚀 Deployment Guide - Jackpot Party Casino

## Prerequisites

### 1. Development Environment
- Node.js 18+ installed
- Expo CLI: `npm install -g @expo/cli`
- EAS CLI: `npm install -g eas-cli`
- Git configured with your GitHub account

### 2. Accounts Required
- **Expo Account**: [expo.dev](https://expo.dev)
- **Apple Developer Account**: [developer.apple.com](https://developer.apple.com) ($99/year)
- **Google Play Console**: [play.google.com/console](https://play.google.com/console) ($25 one-time)
- **Supabase Project**: [supabase.com](https://supabase.com)

## 🔧 Setup Instructions

### 1. Clone and Install
```bash
git clone <your-repo-url>
cd casino
npm install
```

### 2. Environment Configuration
Create `.env` file:
```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Supabase Setup
1. Create new project at [supabase.com](https://supabase.com)
2. Run the SQL schema from `supabase-schema.sql`
3. Configure RLS policies
4. Get your project URL and anon key

### 4. Expo Configuration
```bash
# Login to Expo
expo login

# Initialize EAS
eas login
eas build:configure
```

## 🧪 Testing

### Run Tests Locally
```bash
# Unit tests
npm test

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage

# Type checking
npm run type-check

# Linting
npm run lint
```

### Test on Device
```bash
# Development build
expo start --dev-client

# Preview build
npm run build:preview
```

## 🏗️ Building

### Development Build
```bash
eas build --profile development --platform all
```

### Preview Build (Internal Testing)
```bash
eas build --profile preview --platform all
```

### Production Build
```bash
eas build --profile production --platform all
```

## 📱 App Store Submission

### iOS App Store

1. **Prepare App Store Connect**
   - Create app listing
   - Add screenshots and metadata
   - Set up pricing and availability

2. **Build and Submit**
   ```bash
   # Build for production
   eas build --platform ios --profile production
   
   # Submit to App Store
   eas submit --platform ios --profile production
   ```

3. **Required Information**
   - App name and description
   - Keywords and categories
   - Screenshots (iPhone and iPad)
   - App icon (1024x1024)
   - Privacy policy URL
   - Age rating information

### Google Play Store

1. **Prepare Play Console**
   - Create app listing
   - Add store listing details
   - Set up content rating

2. **Build and Submit**
   ```bash
   # Build for production
   eas build --platform android --profile production
   
   # Submit to Play Store
   eas submit --platform android --profile production
   ```

3. **Required Information**
   - App title and description
   - Screenshots and feature graphic
   - App icon (512x512)
   - Content rating questionnaire
   - Privacy policy URL

## 🔄 CI/CD Pipeline

### GitHub Actions Setup

1. **Repository Secrets**
   Add these secrets to your GitHub repository:
   ```
   EXPO_TOKEN=your_expo_access_token
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   EXPO_APPLE_ID=your_apple_id
   EXPO_ASC_APP_ID=your_app_store_connect_app_id
   EXPO_APPLE_TEAM_ID=your_apple_team_id
   EXPO_ANDROID_SERVICE_ACCOUNT_KEY_PATH=path_to_service_account_key
   ```

2. **Workflow Triggers**
   - **Pull Requests**: Run tests and build preview
   - **Main Branch**: Run tests and build production
   - **Deploy Tag**: Submit to app stores (add `[deploy]` to commit message)

### Manual Deployment
```bash
# Build and deploy in one command
npm run build:production

# Submit to both stores
npm run submit:ios
npm run submit:android
```

## 📊 Monitoring and Analytics

### Supabase Analytics Dashboard
- User registrations and activity
- Game session data
- Purchase analytics
- Quest completion rates

### App Store Analytics
- Download and revenue metrics
- User retention and engagement
- Crash reports and performance

### Custom Analytics Queries
```sql
-- Daily active users
SELECT DATE(created_at) as date, COUNT(DISTINCT user_id) as dau
FROM analytics_events 
WHERE event_type = 'session_start'
AND created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- Revenue by day
SELECT DATE(created_at) as date, SUM(amount) as revenue
FROM purchases 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## 🔒 Security Checklist

- [ ] Environment variables properly configured
- [ ] Supabase RLS policies enabled and tested
- [ ] API keys not exposed in client code
- [ ] Privacy policy and terms of service updated
- [ ] App permissions properly configured
- [ ] Purchase validation implemented
- [ ] User data encryption enabled

## 🚨 Troubleshooting

### Common Build Issues

1. **Metro bundler errors**
   ```bash
   npx expo start --clear
   ```

2. **EAS build failures**
   ```bash
   eas build:list
   eas build:view [build-id]
   ```

3. **Dependency conflicts**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

### App Store Rejection Issues

1. **Privacy Policy**: Ensure privacy policy is accessible and complete
2. **Age Rating**: Set appropriate age rating for gambling simulation
3. **In-App Purchases**: Properly implement purchase restoration
4. **Metadata**: Ensure screenshots and descriptions are accurate

## 📈 Post-Launch

### Monitoring
- Set up crash reporting (Sentry/Bugsnag)
- Monitor app store reviews
- Track key metrics (DAU, retention, revenue)
- Monitor Supabase usage and performance

### Updates
- Regular security updates
- New game features and content
- Bug fixes and performance improvements
- Seasonal events and promotions

### Scaling
- Database optimization as user base grows
- CDN setup for static assets
- Push notification campaigns
- A/B testing for features and monetization

## 📞 Support

For deployment issues:
1. Check Expo documentation: [docs.expo.dev](https://docs.expo.dev)
2. Supabase documentation: [supabase.com/docs](https://supabase.com/docs)
3. App store guidelines: Apple and Google developer documentation

---

**Ready to deploy?** Follow this guide step by step, and your casino app will be live in the app stores! 🎰✨
