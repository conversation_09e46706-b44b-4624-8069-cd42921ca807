import 'dotenv/config';

export default {
  expo: {
    name: 'Jackpot Party Casino',
    slug: 'jackpot-party-casino',
    version: '1.0.0',
    orientation: 'portrait',
    icon: './src/assets/icon.png',
    userInterfaceStyle: 'light',
    newArchEnabled: true,
    splash: {
      image: './src/assets/splash-icon.png',
      resizeMode: 'contain',
      backgroundColor: '#18122B'
    },
    ios: {
      supportsTablet: true,
      bundleIdentifier: 'com.jackpotparty.casino',
      infoPlist: {
        NSUserTrackingUsageDescription: 'This app uses tracking to provide personalized ads and improve your gaming experience.',
        NSCameraUsageDescription: 'Camera access is needed for profile photos.',
        NSPhotoLibraryUsageDescription: 'Photo library access is needed for profile photos.'
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: './src/assets/adaptive-icon.png',
        backgroundColor: '#18122B'
      },
      edgeToEdgeEnabled: true,
      package: 'com.jackpotparty.casino',
      permissions: [
        'CAMERA',
        'READ_EXTERNAL_STORAGE',
        'WRITE_EXTERNAL_STORAGE',
        'NOTIFICATIONS'
      ]
    },
    web: {
      favicon: './src/assets/favicon.png'
    },
    scheme: 'jackpot-party',
    plugins: [
      'expo-router',
      'expo-secure-store',
      'expo-notifications',
      [
        'expo-tracking-transparency',
        {
          userTrackingPermission: 'This app uses tracking to provide personalized ads and improve your gaming experience.'
        }
      ]
    ],
    extra: {
      supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
      supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
      revenuecatApiKey: process.env.EXPO_PUBLIC_REVENUECAT_API_KEY,
      firebaseApiKey: process.env.EXPO_PUBLIC_FIREBASE_API_KEY,
      firebaseProjectId: process.env.EXPO_PUBLIC_FIREBASE_PROJECT_ID,
      appEnv: process.env.EXPO_PUBLIC_APP_ENV || 'development',
      eas: {
        projectId: 'your-eas-project-id-here'
      }
    }
  }
};
