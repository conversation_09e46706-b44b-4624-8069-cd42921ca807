import { useState, useEffect, useCallback } from 'react';
import { supabase } from '../utils/supabase';
import { useAuthContext } from '../contexts/AuthContext';

export interface Quest {
  id: string;
  user_id: string;
  quest_type: 'daily' | 'weekly' | 'achievement';
  title: string;
  description: string;
  target_value: number;
  current_value: number;
  reward_coins: number;
  reward_xp: number;
  reward_gems: number;
  completed: boolean;
  expires_at: string | null;
  created_at: string;
  updated_at: string;
}

interface QuestsState {
  quests: Quest[];
  loading: boolean;
  error: string | null;
}

export const useQuests = () => {
  const { user, isAuthenticated } = useAuthContext();
  const [state, setState] = useState<QuestsState>({
    quests: [],
    loading: true,
    error: null,
  });

  // Load quests from Supabase
  const loadQuests = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setState(prev => ({ ...prev, loading: false, quests: [] }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      const { data, error } = await supabase
        .from('quests')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      setState(prev => ({
        ...prev,
        quests: data || [],
        loading: false,
      }));

    } catch (error) {
      console.error('Error loading quests:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load quests',
      }));
    }
  }, [isAuthenticated, user]);

  // Update quest progress
  const updateQuestProgress = useCallback(async (
    questId: string, 
    progressIncrement: number
  ): Promise<boolean> => {
    if (!isAuthenticated || !user) return false;

    try {
      // Get current quest
      const quest = state.quests.find(q => q.id === questId);
      if (!quest || quest.completed) return false;

      const newProgress = Math.min(
        quest.current_value + progressIncrement,
        quest.target_value
      );
      const isCompleted = newProgress >= quest.target_value;

      // Update quest in database
      const { error } = await supabase
        .from('quests')
        .update({
          current_value: newProgress,
          completed: isCompleted,
          updated_at: new Date().toISOString(),
        })
        .eq('id', questId)
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      // If quest is completed, award rewards
      if (isCompleted && !quest.completed) {
        await awardQuestRewards(quest);
      }

      return true;

    } catch (error) {
      console.error('Error updating quest progress:', error);
      return false;
    }
  }, [isAuthenticated, user, state.quests]);

  // Award quest rewards
  const awardQuestRewards = useCallback(async (quest: Quest) => {
    if (!user) return;

    try {
      // Update user balance with quest rewards
      const { error } = await supabase.rpc('add_quest_rewards', {
        user_uuid: user.id,
        coins_to_add: quest.reward_coins,
        xp_to_add: quest.reward_xp,
        gems_to_add: quest.reward_gems,
      });

      if (error) {
        console.error('Error awarding quest rewards:', error);
      }

    } catch (error) {
      console.error('Error in awardQuestRewards:', error);
    }
  }, [user]);

  // Create daily quests
  const createDailyQuests = useCallback(async () => {
    if (!isAuthenticated || !user) return;

    try {
      // Check if daily quests already exist for today
      const today = new Date().toISOString().split('T')[0];
      const { data: existingQuests } = await supabase
        .from('quests')
        .select('id')
        .eq('user_id', user.id)
        .eq('quest_type', 'daily')
        .gte('created_at', `${today}T00:00:00.000Z`)
        .lt('created_at', `${today}T23:59:59.999Z`);

      if (existingQuests && existingQuests.length > 0) {
        return; // Daily quests already created
      }

      // Create new daily quests
      const dailyQuests = [
        {
          user_id: user.id,
          quest_type: 'daily' as const,
          title: 'Daily Spinner',
          description: 'Spin the slots 10 times',
          target_value: 10,
          current_value: 0,
          reward_coins: 100,
          reward_xp: 50,
          reward_gems: 0,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        },
        {
          user_id: user.id,
          quest_type: 'daily' as const,
          title: 'Lucky Winner',
          description: 'Win 3 slot games',
          target_value: 3,
          current_value: 0,
          reward_coins: 150,
          reward_xp: 75,
          reward_gems: 1,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        },
        {
          user_id: user.id,
          quest_type: 'daily' as const,
          title: 'High Roller',
          description: 'Place bets totaling 500 coins',
          target_value: 500,
          current_value: 0,
          reward_coins: 200,
          reward_xp: 100,
          reward_gems: 2,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        },
      ];

      const { error } = await supabase
        .from('quests')
        .insert(dailyQuests);

      if (error) {
        throw error;
      }

      // Reload quests to include new ones
      await loadQuests();

    } catch (error) {
      console.error('Error creating daily quests:', error);
    }
  }, [isAuthenticated, user, loadQuests]);

  // Complete quest manually (for testing or special cases)
  const completeQuest = useCallback(async (questId: string): Promise<boolean> => {
    if (!isAuthenticated || !user) return false;

    try {
      const quest = state.quests.find(q => q.id === questId);
      if (!quest || quest.completed) return false;

      // Mark quest as completed
      const { error } = await supabase
        .from('quests')
        .update({
          current_value: quest.target_value,
          completed: true,
          updated_at: new Date().toISOString(),
        })
        .eq('id', questId)
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }

      // Award rewards
      await awardQuestRewards(quest);

      return true;

    } catch (error) {
      console.error('Error completing quest:', error);
      return false;
    }
  }, [isAuthenticated, user, state.quests, awardQuestRewards]);

  // Get active quests (not completed and not expired)
  const getActiveQuests = useCallback((): Quest[] => {
    const now = new Date();
    return state.quests.filter(quest => 
      !quest.completed && 
      (!quest.expires_at || new Date(quest.expires_at) > now)
    );
  }, [state.quests]);

  // Get completed quests
  const getCompletedQuests = useCallback((): Quest[] => {
    return state.quests.filter(quest => quest.completed);
  }, [state.quests]);

  // Load quests when user changes
  useEffect(() => {
    loadQuests();
  }, [loadQuests]);

  // Create daily quests on first load
  useEffect(() => {
    if (!state.loading && isAuthenticated && user) {
      createDailyQuests();
    }
  }, [state.loading, isAuthenticated, user, createDailyQuests]);

  // Subscribe to real-time quest updates
  useEffect(() => {
    if (!isAuthenticated || !user) return;

    const subscription = supabase
      .channel(`quests_${user.id}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'quests',
          filter: `user_id=eq.${user.id}`,
        },
        (payload) => {
          console.log('Quest update received:', payload);
          
          setState(prev => {
            const updatedQuests = [...prev.quests];
            
            if (payload.eventType === 'INSERT') {
              updatedQuests.push(payload.new as Quest);
            } else if (payload.eventType === 'UPDATE') {
              const index = updatedQuests.findIndex(q => q.id === payload.new.id);
              if (index !== -1) {
                updatedQuests[index] = payload.new as Quest;
              }
            } else if (payload.eventType === 'DELETE') {
              const index = updatedQuests.findIndex(q => q.id === payload.old.id);
              if (index !== -1) {
                updatedQuests.splice(index, 1);
              }
            }
            
            return {
              ...prev,
              quests: updatedQuests,
            };
          });
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [isAuthenticated, user]);

  return {
    quests: state.quests,
    loading: state.loading,
    error: state.error,
    updateQuestProgress,
    completeQuest,
    getActiveQuests,
    getCompletedQuests,
    createDailyQuests,
    refresh: loadQuests,
  };
};
