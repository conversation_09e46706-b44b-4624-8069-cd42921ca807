import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { fetchWallet } from '../store/slices/walletSlice';

export const useWallet = (deviceId: string | null) => {
  const dispatch = useAppDispatch();
  const { data: wallet, loading, error } = useAppSelector((state) => state.wallet);

  useEffect(() => {
    if (deviceId) {
      dispatch(fetchWallet(deviceId));
    }
  }, [deviceId, dispatch]);

  return {
    wallet,
    loading,
    error,
    coins: wallet?.coins || 0,
    spins: wallet?.spins || 0,
    gems: wallet?.gems || 0,
  };
};