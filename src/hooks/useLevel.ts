import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../utils/supabase';
import { useAuthContext } from '../contexts/AuthContext';

interface LevelState {
  currentXP: number;
  level: number;
  nextLevelXP: number;
  progressToNext: number; // 0-1 percentage
  loading: boolean;
  error: string | null;
}

const LEVEL_STORAGE_KEY = 'user_level_cache';

// Level calculation functions (matching database functions)
const calculateLevel = (xp: number): number => {
  return Math.floor(Math.sqrt(xp / 100)) + 1;
};

const xpForLevel = (level: number): number => {
  return Math.pow(level - 1, 2) * 100;
};

export const useLevel = () => {
  const { user, isAuthenticated } = useAuthContext();
  const [state, setState] = useState<LevelState>({
    currentXP: 0,
    level: 1,
    nextLevelXP: 100,
    progressToNext: 0,
    loading: true,
    error: null,
  });

  // Calculate derived values from XP
  const calculateLevelData = useCallback((xp: number) => {
    const level = calculateLevel(xp);
    const currentLevelXP = xpForLevel(level);
    const nextLevelXP = xpForLevel(level + 1);
    const xpInCurrentLevel = xp - currentLevelXP;
    const xpNeededForNext = nextLevelXP - currentLevelXP;
    const progressToNext = xpNeededForNext > 0 ? xpInCurrentLevel / xpNeededForNext : 0;

    return {
      currentXP: xp,
      level,
      nextLevelXP,
      progressToNext: Math.max(0, Math.min(1, progressToNext)),
    };
  }, []);

  // Load level data from cache and Supabase
  const loadLevel = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setState(prev => ({ ...prev, loading: false, currentXP: 0, level: 1 }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // First, try to load from cache for immediate UI update
      const cachedData = await AsyncStorage.getItem(`${LEVEL_STORAGE_KEY}_${user.id}`);
      if (cachedData) {
        const { xp, level } = JSON.parse(cachedData);
        const levelData = calculateLevelData(xp);
        setState(prev => ({ ...prev, ...levelData, level }));
      }

      // Then fetch from Supabase for accurate data
      const { data, error } = await supabase
        .from('users')
        .select('xp, level')
        .eq('id', user.id)
        .single();

      if (error) {
        throw error;
      }

      const xp = data?.xp || 0;
      const level = data?.level || 1;
      const levelData = calculateLevelData(xp);

      setState(prev => ({ 
        ...prev, 
        ...levelData, 
        level, // Use database level in case of discrepancy
        loading: false 
      }));

      // Update cache
      await AsyncStorage.setItem(
        `${LEVEL_STORAGE_KEY}_${user.id}`, 
        JSON.stringify({ xp, level })
      );

    } catch (error) {
      console.error('Error loading level:', error);
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Failed to load level'
      }));
    }
  }, [isAuthenticated, user, calculateLevelData]);

  // Add XP and handle level ups
  const addXP = useCallback(async (points: number): Promise<{ leveledUp: boolean; newLevel?: number }> => {
    if (!isAuthenticated || !user || points <= 0) {
      return { leveledUp: false };
    }

    try {
      setState(prev => ({ ...prev, error: null }));

      const newXP = state.currentXP + points;
      const oldLevel = state.level;
      const newLevel = calculateLevel(newXP);
      const leveledUp = newLevel > oldLevel;

      // Optimistic update
      const levelData = calculateLevelData(newXP);
      setState(prev => ({ ...prev, ...levelData, level: newLevel }));

      // Update in Supabase (database trigger will auto-update level)
      const { data, error } = await supabase
        .from('users')
        .update({ xp: newXP })
        .eq('id', user.id)
        .select('xp, level')
        .single();

      if (error) {
        throw error;
      }

      const actualXP = data?.xp || newXP;
      const actualLevel = data?.level || newLevel;
      const actualLevelData = calculateLevelData(actualXP);

      setState(prev => ({ 
        ...prev, 
        ...actualLevelData, 
        level: actualLevel 
      }));

      // Update cache
      await AsyncStorage.setItem(
        `${LEVEL_STORAGE_KEY}_${user.id}`, 
        JSON.stringify({ xp: actualXP, level: actualLevel })
      );

      return { 
        leveledUp: actualLevel > oldLevel, 
        newLevel: actualLevel > oldLevel ? actualLevel : undefined 
      };

    } catch (error) {
      console.error('Error adding XP:', error);
      // Revert optimistic update
      const revertedData = calculateLevelData(state.currentXP);
      setState(prev => ({ 
        ...prev, 
        ...revertedData,
        error: error instanceof Error ? error.message : 'Failed to add XP'
      }));
      return { leveledUp: false };
    }
  }, [isAuthenticated, user, state.currentXP, state.level, calculateLevelData]);

  // Get XP needed for a specific level
  const getXPForLevel = useCallback((targetLevel: number): number => {
    return xpForLevel(targetLevel);
  }, []);

  // Get XP needed to reach next level
  const getXPToNextLevel = useCallback((): number => {
    const nextLevel = state.level + 1;
    const xpForNext = xpForLevel(nextLevel);
    return Math.max(0, xpForNext - state.currentXP);
  }, [state.currentXP, state.level]);

  // Check if user is at max level (arbitrary cap at level 100)
  const isMaxLevel = useCallback((): boolean => {
    return state.level >= 100;
  }, [state.level]);

  // Load level data when user changes or component mounts
  useEffect(() => {
    loadLevel();
  }, [loadLevel]);

  // Subscribe to real-time updates
  useEffect(() => {
    if (!isAuthenticated || !user) return;

    const subscription = supabase
      .channel(`level_${user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${user.id}`,
        },
        (payload) => {
          const newXP = payload.new?.xp;
          const newLevel = payload.new?.level;
          
          if (typeof newXP === 'number' && typeof newLevel === 'number') {
            const levelData = calculateLevelData(newXP);
            setState(prev => ({ ...prev, ...levelData, level: newLevel }));
            
            // Update cache
            AsyncStorage.setItem(
              `${LEVEL_STORAGE_KEY}_${user.id}`, 
              JSON.stringify({ xp: newXP, level: newLevel })
            );
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [isAuthenticated, user, calculateLevelData]);

  return {
    currentXP: state.currentXP,
    level: state.level,
    nextLevelXP: state.nextLevelXP,
    progressToNext: state.progressToNext,
    loading: state.loading,
    error: state.error,
    addXP,
    getXPForLevel,
    getXPToNextLevel,
    isMaxLevel,
    refresh: loadLevel,
  };
};
