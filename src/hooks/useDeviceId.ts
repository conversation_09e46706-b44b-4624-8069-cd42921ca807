import { useState, useEffect } from 'react';
import { useAppDispatch } from '../store/hooks';
import { setDeviceId } from '../store/slices/userSlice';
import { getDeviceId } from '../utils/device';

export const useDeviceId = () => {
  const dispatch = useAppDispatch();
  const [deviceId, setLocalDeviceId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initDeviceId = async () => {
      try {
        const id = await getDeviceId();
        setLocalDeviceId(id);
        dispatch(setDeviceId(id));
      } catch (error) {
        console.error('Failed to get device ID:', error);
      } finally {
        setLoading(false);
      }
    };

    initDeviceId();
  }, [dispatch]);

  return { deviceId, loading };
};