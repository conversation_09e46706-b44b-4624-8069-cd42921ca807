import { useState, useEffect } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '../utils/supabase';

export interface AuthState {
  session: Session | null;
  user: User | null;
  loading: boolean;
  initialized: boolean;
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    session: null,
    user: null,
    loading: true,
    initialized: false,
  });

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
        }

        setAuthState({
          session,
          user: session?.user ?? null,
          loading: false,
          initialized: true,
        });
      } catch (error) {
        console.error('Error in getInitialSession:', error);
        setAuthState({
          session: null,
          user: null,
          loading: false,
          initialized: true,
        });
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id);
        
        setAuthState({
          session,
          user: session?.user ?? null,
          loading: false,
          initialized: true,
        });

        // Handle specific auth events
        switch (event) {
          case 'SIGNED_IN':
            console.log('User signed in:', session?.user?.email);
            // Initialize user profile if needed
            await initializeUserProfile(session?.user);
            break;
          case 'SIGNED_OUT':
            console.log('User signed out');
            break;
          case 'TOKEN_REFRESHED':
            console.log('Token refreshed');
            break;
          case 'USER_UPDATED':
            console.log('User updated');
            break;
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const initializeUserProfile = async (user: User | undefined) => {
    if (!user) return;

    try {
      // Check if user profile exists
      const { data: existingProfile, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (fetchError && fetchError.code !== 'PGRST116') {
        // PGRST116 is "not found" error, which is expected for new users
        console.error('Error fetching user profile:', fetchError);
        return;
      }

      if (!existingProfile) {
        // Create new user profile with welcome bonus
        const { error: insertError } = await supabase
          .from('users')
          .insert({
            id: user.id,
            email: user.email!,
            username: user.user_metadata?.username || null,
            coins: 1000, // Welcome bonus
            spins: 10,   // Welcome bonus
            gems: 0,
            xp: 0,
            level: 1,
            daily_streak: 0,
            last_daily_bonus: null,
          });

        if (insertError) {
          console.error('Error creating user profile:', insertError);
        } else {
          console.log('User profile created with welcome bonus');
        }
      }
    } catch (error) {
      console.error('Error in initializeUserProfile:', error);
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        console.error('Error signing out:', error);
        throw error;
      }
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    }
  };

  const refreshSession = async () => {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      if (error) {
        console.error('Error refreshing session:', error);
        throw error;
      }
      return data;
    } catch (error) {
      console.error('Refresh session error:', error);
      throw error;
    }
  };

  return {
    ...authState,
    signOut,
    refreshSession,
    isAuthenticated: !!authState.session,
  };
};
