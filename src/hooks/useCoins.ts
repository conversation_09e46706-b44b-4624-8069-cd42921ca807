import { useState, useEffect, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../utils/supabase';
import { useAuthContext } from '../contexts/AuthContext';

interface CoinsState {
  balance: number;
  loading: boolean;
  error: string | null;
}

const COINS_STORAGE_KEY = 'user_coins_cache';

export const useCoins = () => {
  const { user, isAuthenticated } = useAuthContext();
  const [state, setState] = useState<CoinsState>({
    balance: 0,
    loading: true,
    error: null,
  });

  // Load coins from cache and Supabase
  const loadCoins = useCallback(async () => {
    if (!isAuthenticated || !user) {
      setState(prev => ({ ...prev, loading: false, balance: 0 }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // First, try to load from cache for immediate UI update
      const cachedCoins = await AsyncStorage.getItem(`${COINS_STORAGE_KEY}_${user.id}`);
      if (cachedCoins) {
        setState(prev => ({ ...prev, balance: parseInt(cachedCoins, 10) }));
      }

      // Then fetch from Supabase for accurate data
      const { data, error } = await supabase
        .from('users')
        .select('coins')
        .eq('id', user.id)
        .single();

      if (error) {
        throw error;
      }

      const actualBalance = data?.coins || 0;
      setState(prev => ({ ...prev, balance: actualBalance, loading: false }));

      // Update cache
      await AsyncStorage.setItem(`${COINS_STORAGE_KEY}_${user.id}`, actualBalance.toString());

    } catch (error) {
      console.error('Error loading coins:', error);
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: error instanceof Error ? error.message : 'Failed to load coins'
      }));
    }
  }, [isAuthenticated, user]);

  // Add coins (for wins, purchases, bonuses)
  const add = useCallback(async (amount: number): Promise<boolean> => {
    if (!isAuthenticated || !user || amount <= 0) {
      return false;
    }

    try {
      setState(prev => ({ ...prev, error: null }));

      // Optimistic update
      const newBalance = state.balance + amount;
      setState(prev => ({ ...prev, balance: newBalance }));

      // Update in Supabase
      const { data, error } = await supabase
        .from('users')
        .update({ coins: newBalance })
        .eq('id', user.id)
        .select('coins')
        .single();

      if (error) {
        throw error;
      }

      const actualBalance = data?.coins || newBalance;
      setState(prev => ({ ...prev, balance: actualBalance }));

      // Update cache
      await AsyncStorage.setItem(`${COINS_STORAGE_KEY}_${user.id}`, actualBalance.toString());

      return true;
    } catch (error) {
      console.error('Error adding coins:', error);
      // Revert optimistic update
      setState(prev => ({ 
        ...prev, 
        balance: prev.balance - amount,
        error: error instanceof Error ? error.message : 'Failed to add coins'
      }));
      return false;
    }
  }, [isAuthenticated, user, state.balance]);

  // Subtract coins (for bets, purchases)
  const subtract = useCallback(async (amount: number): Promise<boolean> => {
    if (!isAuthenticated || !user || amount <= 0 || state.balance < amount) {
      return false;
    }

    try {
      setState(prev => ({ ...prev, error: null }));

      // Optimistic update
      const newBalance = state.balance - amount;
      setState(prev => ({ ...prev, balance: newBalance }));

      // Update in Supabase
      const { data, error } = await supabase
        .from('users')
        .update({ coins: newBalance })
        .eq('id', user.id)
        .select('coins')
        .single();

      if (error) {
        throw error;
      }

      const actualBalance = data?.coins || newBalance;
      setState(prev => ({ ...prev, balance: actualBalance }));

      // Update cache
      await AsyncStorage.setItem(`${COINS_STORAGE_KEY}_${user.id}`, actualBalance.toString());

      return true;
    } catch (error) {
      console.error('Error subtracting coins:', error);
      // Revert optimistic update
      setState(prev => ({ 
        ...prev, 
        balance: prev.balance + amount,
        error: error instanceof Error ? error.message : 'Failed to subtract coins'
      }));
      return false;
    }
  }, [isAuthenticated, user, state.balance]);

  // Set coins directly (for admin operations or corrections)
  const set = useCallback(async (amount: number): Promise<boolean> => {
    if (!isAuthenticated || !user || amount < 0) {
      return false;
    }

    try {
      setState(prev => ({ ...prev, error: null }));

      // Update in Supabase
      const { data, error } = await supabase
        .from('users')
        .update({ coins: amount })
        .eq('id', user.id)
        .select('coins')
        .single();

      if (error) {
        throw error;
      }

      const actualBalance = data?.coins || amount;
      setState(prev => ({ ...prev, balance: actualBalance }));

      // Update cache
      await AsyncStorage.setItem(`${COINS_STORAGE_KEY}_${user.id}`, actualBalance.toString());

      return true;
    } catch (error) {
      console.error('Error setting coins:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to set coins'
      }));
      return false;
    }
  }, [isAuthenticated, user]);

  // Check if user can afford a purchase
  const canAfford = useCallback((amount: number): boolean => {
    return state.balance >= amount;
  }, [state.balance]);

  // Load coins when user changes or component mounts
  useEffect(() => {
    loadCoins();
  }, [loadCoins]);

  // Subscribe to real-time updates
  useEffect(() => {
    if (!isAuthenticated || !user) return;

    const subscription = supabase
      .channel(`coins_${user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${user.id}`,
        },
        (payload) => {
          const newCoins = payload.new?.coins;
          if (typeof newCoins === 'number') {
            setState(prev => ({ ...prev, balance: newCoins }));
            // Update cache
            AsyncStorage.setItem(`${COINS_STORAGE_KEY}_${user.id}`, newCoins.toString());
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [isAuthenticated, user]);

  return {
    balance: state.balance,
    loading: state.loading,
    error: state.error,
    add,
    subtract,
    set,
    canAfford,
    refresh: loadCoins,
  };
};
