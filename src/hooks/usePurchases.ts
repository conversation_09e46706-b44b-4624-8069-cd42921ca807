import { useState, useEffect, useCallback } from 'react';
import { Platform, Alert } from 'react-native';
import Constants from 'expo-constants';
import { supabase } from '../utils/supabase';
import { useAuthContext } from '../contexts/AuthContext';

// Note: react-native-purchases would be imported here in a real implementation
// For now, we'll create a mock interface that matches RevenueCat's API

interface PurchasePackage {
  identifier: string;
  packageType: string;
  product: {
    identifier: string;
    description: string;
    title: string;
    price: number;
    priceString: string;
    currencyCode: string;
  };
}

interface CustomerInfo {
  activeSubscriptions: string[];
  allPurchasedProductIdentifiers: string[];
  latestExpirationDate: string | null;
  originalPurchaseDate: string | null;
}

interface PurchaseState {
  offerings: PurchasePackage[];
  loading: boolean;
  error: string | null;
  customerInfo: CustomerInfo | null;
}

// Mock coin packages (in a real app, these would come from RevenueCat)
const MOCK_PACKAGES: PurchasePackage[] = [
  {
    identifier: 'small_coin_pack',
    packageType: 'CUSTOM',
    product: {
      identifier: 'com.jackpotparty.coins.small',
      description: 'Small coin pack with bonus',
      title: 'Starter Pack',
      price: 0.99,
      priceString: '$0.99',
      currencyCode: 'USD',
    },
  },
  {
    identifier: 'medium_coin_pack',
    packageType: 'CUSTOM',
    product: {
      identifier: 'com.jackpotparty.coins.medium',
      description: 'Medium coin pack with great value',
      title: 'Value Pack',
      price: 4.99,
      priceString: '$4.99',
      currencyCode: 'USD',
    },
  },
  {
    identifier: 'large_coin_pack',
    packageType: 'CUSTOM',
    product: {
      identifier: 'com.jackpotparty.coins.large',
      description: 'Large coin pack - Best Value!',
      title: 'Mega Pack',
      price: 9.99,
      priceString: '$9.99',
      currencyCode: 'USD',
    },
  },
  {
    identifier: 'premium_coin_pack',
    packageType: 'CUSTOM',
    product: {
      identifier: 'com.jackpotparty.coins.premium',
      description: 'Premium coin pack with exclusive bonuses',
      title: 'Premium Pack',
      price: 19.99,
      priceString: '$19.99',
      currencyCode: 'USD',
    },
  },
];

// Coin rewards for each package
const PACKAGE_REWARDS = {
  'small_coin_pack': { coins: 1000, gems: 0, spins: 5 },
  'medium_coin_pack': { coins: 6000, gems: 10, spins: 25 },
  'large_coin_pack': { coins: 15000, gems: 25, spins: 50 },
  'premium_coin_pack': { coins: 35000, gems: 75, spins: 100 },
};

export const usePurchases = () => {
  const { user, isAuthenticated } = useAuthContext();
  const [state, setState] = useState<PurchaseState>({
    offerings: [],
    loading: true,
    error: null,
    customerInfo: null,
  });

  // Initialize RevenueCat (mock implementation)
  const initializePurchases = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // In a real implementation, this would be:
      // await Purchases.configure({
      //   apiKey: Constants.expoConfig?.extra?.revenuecatApiKey,
      //   appUserID: user?.id,
      // });

      // Mock initialization delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      setState(prev => ({
        ...prev,
        offerings: MOCK_PACKAGES,
        loading: false,
      }));

    } catch (error) {
      console.error('Error initializing purchases:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to initialize purchases',
      }));
    }
  }, [user]);

  // Load current offerings
  const loadOfferings = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));

      // In a real implementation:
      // const offerings = await Purchases.getOfferings();
      // const packages = offerings.current?.availablePackages || [];

      setState(prev => ({
        ...prev,
        offerings: MOCK_PACKAGES,
        loading: false,
      }));

    } catch (error) {
      console.error('Error loading offerings:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Failed to load offerings',
      }));
    }
  }, []);

  // Purchase a package
  const purchase = useCallback(async (packageIdentifier: string): Promise<boolean> => {
    if (!isAuthenticated || !user) {
      Alert.alert('Error', 'Please sign in to make purchases');
      return false;
    }

    try {
      setState(prev => ({ ...prev, error: null }));

      const packageToPurchase = state.offerings.find(pkg => pkg.identifier === packageIdentifier);
      if (!packageToPurchase) {
        throw new Error('Package not found');
      }

      // Show confirmation dialog
      const confirmed = await new Promise<boolean>((resolve) => {
        Alert.alert(
          'Confirm Purchase',
          `Purchase ${packageToPurchase.product.title} for ${packageToPurchase.product.priceString}?`,
          [
            { text: 'Cancel', style: 'cancel', onPress: () => resolve(false) },
            { text: 'Purchase', onPress: () => resolve(true) },
          ]
        );
      });

      if (!confirmed) {
        return false;
      }

      // In a real implementation:
      // const { customerInfo, productIdentifier } = await Purchases.purchasePackage(packageToPurchase);

      // Mock purchase processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate mock transaction ID
      const transactionId = `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Record purchase in database
      const rewards = PACKAGE_REWARDS[packageIdentifier as keyof typeof PACKAGE_REWARDS];
      if (!rewards) {
        throw new Error('Invalid package rewards');
      }

      const { error: purchaseError } = await supabase
        .from('purchases')
        .insert({
          user_id: user.id,
          product_id: packageToPurchase.product.identifier,
          transaction_id: transactionId,
          amount: packageToPurchase.product.price,
          currency: packageToPurchase.product.currencyCode,
          coins_received: rewards.coins,
          gems_received: rewards.gems,
          spins_received: rewards.spins,
        });

      if (purchaseError) {
        throw purchaseError;
      }

      // Update user balance
      const { error: updateError } = await supabase.rpc('add_purchase_rewards', {
        user_uuid: user.id,
        coins_to_add: rewards.coins,
        gems_to_add: rewards.gems,
        spins_to_add: rewards.spins,
      });

      if (updateError) {
        console.error('Error updating user balance:', updateError);
        // Don't throw here as purchase was recorded
      }

      Alert.alert(
        'Purchase Successful!',
        `You received:\n• ${rewards.coins.toLocaleString()} coins\n• ${rewards.gems} gems\n• ${rewards.spins} free spins`
      );

      return true;

    } catch (error) {
      console.error('Purchase error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Purchase failed';
      
      Alert.alert('Purchase Failed', errorMessage);
      setState(prev => ({ ...prev, error: errorMessage }));
      
      return false;
    }
  }, [isAuthenticated, user, state.offerings]);

  // Restore purchases
  const restorePurchases = useCallback(async (): Promise<boolean> => {
    if (!isAuthenticated || !user) {
      Alert.alert('Error', 'Please sign in to restore purchases');
      return false;
    }

    try {
      setState(prev => ({ ...prev, error: null }));

      // In a real implementation:
      // const customerInfo = await Purchases.restorePurchases();

      // Mock restore process
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Check for any unprocessed purchases in the database
      const { data: purchases, error } = await supabase
        .from('purchases')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        throw error;
      }

      const purchaseCount = purchases?.length || 0;
      
      Alert.alert(
        'Restore Complete',
        purchaseCount > 0 
          ? `Found ${purchaseCount} previous purchases`
          : 'No previous purchases found'
      );

      return true;

    } catch (error) {
      console.error('Restore purchases error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Restore failed';
      
      Alert.alert('Restore Failed', errorMessage);
      setState(prev => ({ ...prev, error: errorMessage }));
      
      return false;
    }
  }, [isAuthenticated, user]);

  // Get customer info
  const getCustomerInfo = useCallback(async () => {
    try {
      // In a real implementation:
      // const customerInfo = await Purchases.getCustomerInfo();
      
      // Mock customer info
      const mockCustomerInfo: CustomerInfo = {
        activeSubscriptions: [],
        allPurchasedProductIdentifiers: [],
        latestExpirationDate: null,
        originalPurchaseDate: null,
      };

      setState(prev => ({ ...prev, customerInfo: mockCustomerInfo }));
      return mockCustomerInfo;

    } catch (error) {
      console.error('Error getting customer info:', error);
      return null;
    }
  }, []);

  // Initialize when user changes
  useEffect(() => {
    if (isAuthenticated && user) {
      initializePurchases();
    }
  }, [isAuthenticated, user, initializePurchases]);

  return {
    offerings: state.offerings,
    loading: state.loading,
    error: state.error,
    customerInfo: state.customerInfo,
    purchase,
    restorePurchases,
    loadOfferings,
    getCustomerInfo,
  };
};
