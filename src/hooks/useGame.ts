import { useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { spin, claimDailyBonus, setSpinning } from '../store/slices/gameSlice';
import { updateBalance } from '../store/slices/walletSlice';
import { GAME_CONFIG } from '../constants/game';

export const useGame = (deviceId: string | null) => {
  const dispatch = useAppDispatch();
  const game = useAppSelector((state) => state.game);
  const { coins } = useAppSelector((state) => state.wallet.data || { coins: 0 });

  const handleSpin = useCallback(async () => {
    if (!deviceId || game.spinning || coins < GAME_CONFIG.MIN_BET) {
      return;
    }

    dispatch(setSpinning(true));
    
    try {
      const result = await dispatch(spin(deviceId)).unwrap();
      
      // Update local balance after spin
      dispatch(updateBalance({ coins: result.newBalance }));
      
      // Add delay for animation
      setTimeout(() => {
        dispatch(setSpinning(false));
      }, GAME_CONFIG.SPIN_DURATION);
    } catch (error) {
      dispatch(setSpinning(false));
      console.error('Spin failed:', error);
    }
  }, [deviceId, game.spinning, coins, dispatch]);

  const handleClaimBonus = useCallback(async () => {
    if (!deviceId || !game.dailyBonus.available) {
      return;
    }

    try {
      const result = await dispatch(claimDailyBonus(deviceId)).unwrap();
      dispatch(updateBalance({ coins: result.newBalance }));
    } catch (error) {
      console.error('Failed to claim bonus:', error);
    }
  }, [deviceId, game.dailyBonus.available, dispatch]);

  return {
    ...game,
    canSpin: !game.spinning && coins >= GAME_CONFIG.MIN_BET,
    handleSpin,
    handleClaimBonus,
  };
};