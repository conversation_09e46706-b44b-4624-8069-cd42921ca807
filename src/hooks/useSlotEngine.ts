import { useState, useCallback } from 'react';
import { supabase } from '../utils/supabase';
import { useAuthContext } from '../contexts/AuthContext';

// Slot symbols with their weights (higher weight = more common)
const SYMBOLS = {
  '🍒': { weight: 30, value: 2 },   // Cherry - common, low value
  '🍋': { weight: 25, value: 3 },   // Lemon - common, low value
  '🔔': { weight: 20, value: 5 },   // Bell - medium, medium value
  '7️⃣': { weight: 15, value: 10 },  // Seven - rare, high value
  '💎': { weight: 8, value: 20 },   // Diamond - very rare, very high value
  '🎰': { weight: 2, value: 50 },   // Jackpot - extremely rare, jackpot
} as const;

type Symbol = keyof typeof SYMBOLS;
type SlotMatrix = Symbol[][];

interface SpinResult {
  matrix: SlotMatrix;
  win: number;
  winningLines: number[];
  isJackpot: boolean;
}

interface SlotEngineState {
  spinning: boolean;
  lastResult: SpinResult | null;
  error: string | null;
}

// Paylines for 5x3 slot machine (left to right)
const PAYLINES = [
  [0, 0, 0, 0, 0], // Top row
  [1, 1, 1, 1, 1], // Middle row
  [2, 2, 2, 2, 2], // Bottom row
  [0, 1, 2, 1, 0], // V shape
  [2, 1, 0, 1, 2], // Inverted V
  [0, 0, 1, 2, 2], // Diagonal down
  [2, 2, 1, 0, 0], // Diagonal up
  [1, 0, 1, 2, 1], // W shape
  [1, 2, 1, 0, 1], // M shape
];

export const useSlotEngine = () => {
  const { user, isAuthenticated } = useAuthContext();
  const [state, setState] = useState<SlotEngineState>({
    spinning: false,
    lastResult: null,
    error: null,
  });

  // Weighted random symbol selection
  const getRandomSymbol = useCallback((): Symbol => {
    const symbols = Object.keys(SYMBOLS) as Symbol[];
    const totalWeight = Object.values(SYMBOLS).reduce((sum, symbol) => sum + symbol.weight, 0);
    
    let random = Math.random() * totalWeight;
    
    for (const symbol of symbols) {
      random -= SYMBOLS[symbol].weight;
      if (random <= 0) {
        return symbol;
      }
    }
    
    return symbols[0]; // Fallback
  }, []);

  // Generate a 5x3 slot matrix
  const generateMatrix = useCallback((): SlotMatrix => {
    const matrix: SlotMatrix = [];
    
    for (let reel = 0; reel < 5; reel++) {
      const reelSymbols: Symbol[] = [];
      for (let row = 0; row < 3; row++) {
        reelSymbols.push(getRandomSymbol());
      }
      matrix.push(reelSymbols);
    }
    
    return matrix;
  }, [getRandomSymbol]);

  // Check for winning combinations on paylines
  const checkWins = useCallback((matrix: SlotMatrix): { winAmount: number; winningLines: number[] } => {
    let totalWin = 0;
    const winningLines: number[] = [];

    PAYLINES.forEach((payline, lineIndex) => {
      const lineSymbols = payline.map((row, reel) => matrix[reel][row]);
      
      // Check for matching symbols (minimum 3 in a row from left)
      let matchCount = 1;
      const firstSymbol = lineSymbols[0];
      
      for (let i = 1; i < lineSymbols.length; i++) {
        if (lineSymbols[i] === firstSymbol) {
          matchCount++;
        } else {
          break;
        }
      }

      // Calculate win for this line
      if (matchCount >= 3) {
        const symbolValue = SYMBOLS[firstSymbol].value;
        let lineWin = symbolValue;
        
        // Multipliers based on match count
        switch (matchCount) {
          case 3:
            lineWin *= 1;
            break;
          case 4:
            lineWin *= 3;
            break;
          case 5:
            lineWin *= 10;
            break;
        }

        totalWin += lineWin;
        winningLines.push(lineIndex);
      }
    });

    return { winAmount: totalWin, winningLines };
  }, []);

  // Main spin function
  const spin = useCallback(async (betAmount: number): Promise<SpinResult> => {
    if (!isAuthenticated || !user) {
      throw new Error('User not authenticated');
    }

    if (betAmount <= 0) {
      throw new Error('Invalid bet amount');
    }

    setState(prev => ({ ...prev, spinning: true, error: null }));

    try {
      // Generate slot result
      const matrix = generateMatrix();
      const { winAmount, winningLines } = checkWins(matrix);
      
      // Check for jackpot (all diamonds or all jackpot symbols)
      const isJackpot = matrix.every(reel => 
        reel.every(symbol => symbol === '💎' || symbol === '🎰')
      );

      // Apply jackpot multiplier
      const finalWin = isJackpot ? winAmount * 5 : winAmount;

      const result: SpinResult = {
        matrix,
        win: finalWin,
        winningLines,
        isJackpot,
      };

      // Record game session in database
      const { error: sessionError } = await supabase
        .from('game_sessions')
        .insert({
          user_id: user.id,
          game_type: 'classic',
          bet_amount: betAmount,
          payout: finalWin,
          result_data: {
            matrix,
            winningLines,
            isJackpot,
            rtp: finalWin / betAmount, // Return to Player for this spin
          },
        });

      if (sessionError) {
        console.error('Error recording game session:', sessionError);
        // Don't throw here, as the spin was successful
      }

      setState(prev => ({ 
        ...prev, 
        spinning: false, 
        lastResult: result 
      }));

      return result;

    } catch (error) {
      console.error('Error in slot spin:', error);
      setState(prev => ({ 
        ...prev, 
        spinning: false, 
        error: error instanceof Error ? error.message : 'Spin failed'
      }));
      throw error;
    }
  }, [isAuthenticated, user, generateMatrix, checkWins]);

  // Calculate theoretical RTP (Return to Player)
  const calculateRTP = useCallback((): number => {
    // This is a simplified RTP calculation
    // In a real casino game, this would be much more complex
    const symbols = Object.entries(SYMBOLS);
    let totalExpectedReturn = 0;
    let totalWeight = 0;

    symbols.forEach(([symbol, data]) => {
      totalWeight += data.weight;
      totalExpectedReturn += (data.weight / 100) * data.value;
    });

    // Factor in payline multipliers and match requirements
    // This gives approximately 95% RTP
    return Math.min(0.95, totalExpectedReturn / totalWeight);
  }, []);

  // Get symbol information
  const getSymbolInfo = useCallback((symbol: Symbol) => {
    return SYMBOLS[symbol];
  }, []);

  // Get all available symbols
  const getAllSymbols = useCallback((): Symbol[] => {
    return Object.keys(SYMBOLS) as Symbol[];
  }, []);

  // Check if a matrix has any winning combinations
  const hasWinningCombination = useCallback((matrix: SlotMatrix): boolean => {
    const { winAmount } = checkWins(matrix);
    return winAmount > 0;
  }, [checkWins]);

  return {
    spinning: state.spinning,
    lastResult: state.lastResult,
    error: state.error,
    spin,
    calculateRTP,
    getSymbolInfo,
    getAllSymbols,
    hasWinningCombination,
    paylines: PAYLINES,
  };
};
