import { useCallback } from 'react';
import { Platform } from 'react-native';
import Constants from 'expo-constants';
import * as Application from 'expo-application';
import { supabase } from '../utils/supabase';
import { useAuthContext } from '../contexts/AuthContext';

export interface AnalyticsEvent {
  event_type: 'spin' | 'win' | 'purchase' | 'level_up' | 'daily_collect' | 'app_open' | 'session_start' | 'quest_complete';
  event_data: Record<string, any>;
  user_id?: string;
  session_id: string;
  timestamp: string;
  app_version: string;
  platform: string;
  device_info: Record<string, any>;
}

// Generate a session ID that persists for the app session
let currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

export const useAnalytics = () => {
  const { user, isAuthenticated } = useAuthContext();

  // Get device and app information
  const getDeviceInfo = useCallback(() => {
    return {
      platform: Platform.OS,
      version: Platform.Version,
      appVersion: Application.nativeApplicationVersion || '1.0.0',
      buildVersion: Application.nativeBuildVersion || '1',
      deviceName: Constants.deviceName,
      isDevice: Constants.isDevice,
      screenDimensions: {
        width: Constants.screenDimensions?.width,
        height: Constants.screenDimensions?.height,
      },
    };
  }, []);

  // Track an analytics event
  const trackEvent = useCallback(async (
    eventType: AnalyticsEvent['event_type'],
    eventData: Record<string, any> = {}
  ): Promise<boolean> => {
    try {
      const analyticsEvent: AnalyticsEvent = {
        event_type: eventType,
        event_data: eventData,
        user_id: user?.id,
        session_id: currentSessionId,
        timestamp: new Date().toISOString(),
        app_version: Application.nativeApplicationVersion || '1.0.0',
        platform: Platform.OS,
        device_info: getDeviceInfo(),
      };

      // Store in Supabase analytics table
      const { error } = await supabase
        .from('analytics_events')
        .insert(analyticsEvent);

      if (error) {
        console.error('Analytics tracking error:', error);
        return false;
      }

      console.log(`📊 Analytics: ${eventType}`, eventData);
      return true;

    } catch (error) {
      console.error('Error tracking analytics event:', error);
      return false;
    }
  }, [user, getDeviceInfo]);

  // Specific event tracking methods
  const trackSpin = useCallback(async (betAmount: number, gameType: string = 'classic') => {
    return trackEvent('spin', {
      bet_amount: betAmount,
      game_type: gameType,
    });
  }, [trackEvent]);

  const trackWin = useCallback(async (
    winAmount: number,
    betAmount: number,
    gameType: string = 'classic',
    isJackpot: boolean = false
  ) => {
    return trackEvent('win', {
      win_amount: winAmount,
      bet_amount: betAmount,
      game_type: gameType,
      is_jackpot: isJackpot,
      multiplier: winAmount / betAmount,
    });
  }, [trackEvent]);

  const trackPurchase = useCallback(async (
    productId: string,
    price: number,
    currency: string = 'USD',
    coinsReceived: number
  ) => {
    return trackEvent('purchase', {
      product_id: productId,
      price,
      currency,
      coins_received: coinsReceived,
    });
  }, [trackEvent]);

  const trackLevelUp = useCallback(async (
    oldLevel: number,
    newLevel: number,
    totalXP: number
  ) => {
    return trackEvent('level_up', {
      old_level: oldLevel,
      new_level: newLevel,
      total_xp: totalXP,
      levels_gained: newLevel - oldLevel,
    });
  }, [trackEvent]);

  const trackDailyCollect = useCallback(async (
    bonusAmount: number,
    streakCount: number
  ) => {
    return trackEvent('daily_collect', {
      bonus_amount: bonusAmount,
      streak_count: streakCount,
    });
  }, [trackEvent]);

  const trackQuestComplete = useCallback(async (
    questId: string,
    questType: string,
    rewardCoins: number,
    rewardXP: number
  ) => {
    return trackEvent('quest_complete', {
      quest_id: questId,
      quest_type: questType,
      reward_coins: rewardCoins,
      reward_xp: rewardXP,
    });
  }, [trackEvent]);

  const trackAppOpen = useCallback(async () => {
    return trackEvent('app_open', {
      is_authenticated: isAuthenticated,
      user_level: user?.user_metadata?.level || 1,
    });
  }, [trackEvent, isAuthenticated, user]);

  const trackSessionStart = useCallback(async () => {
    // Generate new session ID
    currentSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    return trackEvent('session_start', {
      is_authenticated: isAuthenticated,
      user_level: user?.user_metadata?.level || 1,
    });
  }, [trackEvent, isAuthenticated, user]);

  // Batch tracking for multiple events (useful for offline scenarios)
  const trackBatch = useCallback(async (events: Array<{
    eventType: AnalyticsEvent['event_type'];
    eventData: Record<string, any>;
  }>): Promise<boolean> => {
    try {
      const analyticsEvents: AnalyticsEvent[] = events.map(({ eventType, eventData }) => ({
        event_type: eventType,
        event_data: eventData,
        user_id: user?.id,
        session_id: currentSessionId,
        timestamp: new Date().toISOString(),
        app_version: Application.nativeApplicationVersion || '1.0.0',
        platform: Platform.OS,
        device_info: getDeviceInfo(),
      }));

      const { error } = await supabase
        .from('analytics_events')
        .insert(analyticsEvents);

      if (error) {
        console.error('Batch analytics tracking error:', error);
        return false;
      }

      console.log(`📊 Analytics: Tracked ${events.length} events`);
      return true;

    } catch (error) {
      console.error('Error tracking batch analytics events:', error);
      return false;
    }
  }, [user, getDeviceInfo]);

  // Get analytics summary for the current user
  const getAnalyticsSummary = useCallback(async (days: number = 7) => {
    if (!user) return null;

    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const { data, error } = await supabase
        .from('analytics_events')
        .select('event_type, event_data, timestamp')
        .eq('user_id', user.id)
        .gte('timestamp', startDate.toISOString())
        .order('timestamp', { ascending: false });

      if (error) {
        throw error;
      }

      // Process analytics data
      const summary = {
        totalEvents: data?.length || 0,
        spins: data?.filter(e => e.event_type === 'spin').length || 0,
        wins: data?.filter(e => e.event_type === 'win').length || 0,
        purchases: data?.filter(e => e.event_type === 'purchase').length || 0,
        levelUps: data?.filter(e => e.event_type === 'level_up').length || 0,
        dailyCollects: data?.filter(e => e.event_type === 'daily_collect').length || 0,
        questCompletes: data?.filter(e => e.event_type === 'quest_complete').length || 0,
        sessions: new Set(data?.map(e => e.event_data?.session_id)).size || 0,
      };

      return summary;

    } catch (error) {
      console.error('Error getting analytics summary:', error);
      return null;
    }
  }, [user]);

  return {
    trackEvent,
    trackSpin,
    trackWin,
    trackPurchase,
    trackLevelUp,
    trackDailyCollect,
    trackQuestComplete,
    trackAppOpen,
    trackSessionStart,
    trackBatch,
    getAnalyticsSummary,
    sessionId: currentSessionId,
  };
};
