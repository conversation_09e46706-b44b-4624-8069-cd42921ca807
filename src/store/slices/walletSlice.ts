import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { walletService, Wallet } from '../../services/wallet.service';

interface WalletState {
  data: Wallet | null;
  loading: boolean;
  error: string | null;
}

const initialState: WalletState = {
  data: null,
  loading: false,
  error: null,
};

export const fetchWallet = createAsyncThunk(
  'wallet/fetch',
  async (deviceId: string) => {
    return await walletService.getWallet(deviceId);
  }
);

export const updateWallet = createAsyncThunk(
  'wallet/update',
  async ({ deviceId, updates }: { deviceId: string; updates: Partial<Omit<Wallet, 'deviceId'>> }) => {
    return await walletService.updateWallet(deviceId, updates);
  }
);

const walletSlice = createSlice({
  name: 'wallet',
  initialState,
  reducers: {
    updateBalance: (state, action: PayloadAction<{ coins?: number; spins?: number; gems?: number }>) => {
      if (state.data) {
        state.data = { ...state.data, ...action.payload };
      }
    },
    resetWallet: (state) => {
      state.data = null;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch wallet
      .addCase(fetchWallet.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWallet.fulfilled, (state, action) => {
        state.loading = false;
        state.data = action.payload;
      })
      .addCase(fetchWallet.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch wallet';
      })
      // Update wallet
      .addCase(updateWallet.fulfilled, (state, action) => {
        state.data = action.payload;
      });
  },
});

export const { updateBalance, resetWallet } = walletSlice.actions;
export default walletSlice.reducer;