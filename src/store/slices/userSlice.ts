import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UserState {
  deviceId: string | null;
  profile: {
    displayName: string;
    avatar: string;
    xp: number;
    level: number;
  } | null;
}

const initialState: UserState = {
  deviceId: null,
  profile: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setDeviceId: (state, action: PayloadAction<string>) => {
      state.deviceId = action.payload;
    },
    setProfile: (state, action: PayloadAction<UserState['profile']>) => {
      state.profile = action.payload;
    },
    updateXP: (state, action: PayloadAction<number>) => {
      if (state.profile) {
        state.profile.xp += action.payload;
        // Simple level calculation
        state.profile.level = Math.floor(state.profile.xp / 1000) + 1;
      }
    },
  },
});

export const { setDeviceId, setProfile, updateXP } = userSlice.actions;
export default userSlice.reducer;