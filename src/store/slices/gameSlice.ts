import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { gameService, SpinResult, DailyBonusResult } from '../../services/game.service';
import { GAME_CONFIG } from '../../constants/game';

interface GameState {
  reels: string[][];
  spinning: boolean;
  lastPayout: number;
  winningLines: number[];
  dailyBonus: {
    available: boolean;
    streak: number;
    lastClaimed: string | null;
  };
  error: string | null;
}

const initialState: GameState = {
  reels: GAME_CONFIG.DEFAULT_REELS,
  spinning: false,
  lastPayout: 0,
  winningLines: [],
  dailyBonus: {
    available: true,
    streak: 0,
    lastClaimed: null,
  },
  error: null,
};

export const spin = createAsyncThunk(
  'game/spin',
  async (deviceId: string) => {
    return await gameService.spin(deviceId);
  }
);

export const claimDailyBonus = createAsyncThunk(
  'game/claimDailyBonus',
  async (deviceId: string) => {
    return await gameService.claimDailyBonus(deviceId);
  }
);

const gameSlice = createSlice({
  name: 'game',
  initialState,
  reducers: {
    setSpinning: (state, action: PayloadAction<boolean>) => {
      state.spinning = action.payload;
    },
    resetGame: (state) => {
      state.reels = GAME_CONFIG.DEFAULT_REELS;
      state.lastPayout = 0;
      state.winningLines = [];
    },
  },
  extraReducers: (builder) => {
    builder
      // Spin
      .addCase(spin.pending, (state) => {
        state.spinning = true;
        state.error = null;
      })
      .addCase(spin.fulfilled, (state, action) => {
        state.spinning = false;
        state.reels = action.payload.reels;
        state.lastPayout = action.payload.payout;
        state.winningLines = action.payload.winningLines || [];
      })
      .addCase(spin.rejected, (state, action) => {
        state.spinning = false;
        state.error = action.error.message || 'Spin failed';
      })
      // Daily Bonus
      .addCase(claimDailyBonus.fulfilled, (state, action) => {
        state.dailyBonus.available = false;
        state.dailyBonus.streak = action.payload.streak;
        state.dailyBonus.lastClaimed = new Date().toISOString();
      });
  },
});

export const { setSpinning, resetGame } = gameSlice.actions;
export default gameSlice.reducer;