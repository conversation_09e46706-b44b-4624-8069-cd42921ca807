import { apiService } from './api';
import { API_CONFIG } from '../constants/api';

export interface Wallet {
  deviceId: string;
  coins: number;
  spins: number;
  gems: number;
}

export class WalletService {
  async getWallet(deviceId: string): Promise<Wallet> {
    return apiService.post<Wallet>(API_CONFIG.ENDPOINTS.WALLET, { deviceId });
  }

  async updateWallet(deviceId: string, updates: Partial<Omit<Wallet, 'deviceId'>>): Promise<Wallet> {
    return apiService.put<Wallet>(API_CONFIG.ENDPOINTS.WALLET, { deviceId, ...updates });
  }
}

export const walletService = new WalletService();