import { apiService } from './api';
import { API_CONFIG } from '../constants/api';

export interface SpinResult {
  reels: string[][];
  payout: number;
  newBalance: number;
  winningLines?: number[];
}

export interface DailyBonusResult {
  newBalance: number;
  streak: number;
  nextBonusAt: string;
}

export class GameService {
  async spin(deviceId: string): Promise<SpinResult> {
    return apiService.post<SpinResult>(API_CONFIG.ENDPOINTS.SPIN, { deviceId });
  }

  async claimDailyBonus(deviceId: string): Promise<DailyBonusResult> {
    return apiService.post<DailyBonusResult>(API_CONFIG.ENDPOINTS.DAILY_BONUS, { deviceId });
  }
}

export const gameService = new GameService();