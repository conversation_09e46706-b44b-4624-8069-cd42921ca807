import React from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { Colors, Shadows } from '../../constants/colors';
import { usePurchases } from '../../hooks/usePurchases';
import { LoadingScreen } from './LoadingScreen';

interface PurchaseModalProps {
  visible: boolean;
  onClose: () => void;
}

interface BundleTileProps {
  title: string;
  description: string;
  price: string;
  coins: number;
  gems?: number;
  spins?: number;
  onPress: () => void;
  loading?: boolean;
  mostPopular?: boolean;
  bestValue?: boolean;
}

const BundleTile: React.FC<BundleTileProps> = ({
  title,
  description,
  price,
  coins,
  gems = 0,
  spins = 0,
  onPress,
  loading = false,
  mostPopular = false,
  bestValue = false,
}) => {
  return (
    <TouchableOpacity
      style={[
        styles.bundleTile,
        mostPopular && styles.mostPopularTile,
        bestValue && styles.bestValueTile,
      ]}
      onPress={onPress}
      disabled={loading}
      activeOpacity={0.8}
    >
      {mostPopular && (
        <View style={styles.ribbon}>
          <Text style={styles.ribbonText}>MOST POPULAR</Text>
        </View>
      )}
      
      {bestValue && (
        <View style={[styles.ribbon, styles.bestValueRibbon]}>
          <Text style={styles.ribbonText}>BEST VALUE</Text>
        </View>
      )}
      
      <View style={styles.bundleHeader}>
        <Text style={styles.bundleTitle}>{title}</Text>
        <Text style={styles.bundlePrice}>{price}</Text>
      </View>
      
      <Text style={styles.bundleDescription}>{description}</Text>
      
      <View style={styles.rewardsContainer}>
        <View style={styles.rewardItem}>
          <Text style={styles.rewardEmoji}>🪙</Text>
          <Text style={styles.rewardText}>{coins.toLocaleString()}</Text>
        </View>
        
        {gems > 0 && (
          <View style={styles.rewardItem}>
            <Text style={styles.rewardEmoji}>💎</Text>
            <Text style={styles.rewardText}>{gems}</Text>
          </View>
        )}
        
        {spins > 0 && (
          <View style={styles.rewardItem}>
            <Text style={styles.rewardEmoji}>🎰</Text>
            <Text style={styles.rewardText}>{spins}</Text>
          </View>
        )}
      </View>
      
      <View style={styles.purchaseButton}>
        <Text style={styles.purchaseButtonText}>
          {loading ? 'Processing...' : 'Purchase'}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

export const PurchaseModal: React.FC<PurchaseModalProps> = ({
  visible,
  onClose,
}) => {
  const { offerings, loading, purchase, restorePurchases } = usePurchases();

  const handlePurchase = async (packageId: string) => {
    const success = await purchase(packageId);
    if (success) {
      onClose();
    }
  };

  const handleRestore = async () => {
    await restorePurchases();
  };

  if (loading) {
    return (
      <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
        <SafeAreaView style={styles.container}>
          <LoadingScreen />
        </SafeAreaView>
      </Modal>
    );
  }

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>💰 Coin Shop</Text>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>
        
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Text style={styles.subtitle}>
            Get more coins to keep playing and unlock amazing rewards!
          </Text>
          
          <View style={styles.bundlesContainer}>
            {offerings.map((offering, index) => {
              const rewards = {
                small_coin_pack: { coins: 1000, gems: 0, spins: 5 },
                medium_coin_pack: { coins: 6000, gems: 10, spins: 25 },
                large_coin_pack: { coins: 15000, gems: 25, spins: 50 },
                premium_coin_pack: { coins: 35000, gems: 75, spins: 100 },
              }[offering.identifier as keyof typeof rewards] || { coins: 0, gems: 0, spins: 0 };

              return (
                <BundleTile
                  key={offering.identifier}
                  title={offering.product.title}
                  description={offering.product.description}
                  price={offering.product.priceString}
                  coins={rewards.coins}
                  gems={rewards.gems}
                  spins={rewards.spins}
                  onPress={() => handlePurchase(offering.identifier)}
                  mostPopular={index === 1} // Medium pack
                  bestValue={index === 2} // Large pack
                />
              );
            })}
          </View>
          
          <View style={styles.footer}>
            <TouchableOpacity style={styles.restoreButton} onPress={handleRestore}>
              <Text style={styles.restoreButtonText}>Restore Purchases</Text>
            </TouchableOpacity>
            
            <Text style={styles.disclaimer}>
              Purchases are processed securely through your app store account.
              All sales are final.
            </Text>
          </View>
        </ScrollView>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.card,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.card,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
    color: Colors.text,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  bundlesContainer: {
    gap: 16,
  },
  bundleTile: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    position: 'relative',
    ...Shadows.card,
  },
  mostPopularTile: {
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  bestValueTile: {
    borderWidth: 2,
    borderColor: Colors.success,
  },
  ribbon: {
    position: 'absolute',
    top: -1,
    right: 12,
    backgroundColor: Colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
    zIndex: 1,
  },
  bestValueRibbon: {
    backgroundColor: Colors.success,
  },
  ribbonText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.text,
  },
  bundleHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  bundleTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
  },
  bundlePrice: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  bundleDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 16,
  },
  rewardsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16,
  },
  rewardItem: {
    alignItems: 'center',
  },
  rewardEmoji: {
    fontSize: 24,
    marginBottom: 4,
  },
  rewardText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.text,
  },
  purchaseButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    ...Shadows.button,
  },
  purchaseButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.background,
  },
  footer: {
    marginTop: 32,
    alignItems: 'center',
  },
  restoreButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  restoreButtonText: {
    fontSize: 16,
    color: Colors.primary,
    textDecorationLine: 'underline',
  },
  disclaimer: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
    paddingHorizontal: 20,
  },
});
