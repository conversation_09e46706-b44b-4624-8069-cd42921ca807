import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../../constants/colors';
import { formatNumber } from '../../utils/format';

interface BalanceDisplayProps {
  coins: number;
  spins: number;
  gems?: number;
}

export const BalanceDisplay: React.FC<BalanceDisplayProps> = ({ coins, spins, gems }) => {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>
        🪙 {formatNumber(coins)} | 🎰 {spins}
        {gems !== undefined && gems > 0 && ` | 💎 ${formatNumber(gems)}`}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  text: {
    color: Colors.text,
    fontSize: 20,
  },
});