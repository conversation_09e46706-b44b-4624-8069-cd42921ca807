import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Colors, Shadows } from '../../constants/colors';

interface Quest {
  id: string;
  title: string;
  description: string;
  progress: number; // 0-1
  reward_coins: number;
  reward_xp: number;
  completed: boolean;
}

interface NewQuestBannerProps {
  quest?: Quest;
  onPress?: () => void;
  visible?: boolean;
}

export const NewQuestBanner: React.FC<NewQuestBannerProps> = ({
  quest,
  onPress,
  visible = true,
}) => {
  if (!visible || !quest || quest.completed) {
    return null;
  }

  const progressPercentage = Math.round(quest.progress * 100);

  return (
    <TouchableOpacity
      style={styles.container}
      onPress={onPress}
      activeOpacity={0.8}
    >
      <View style={styles.header}>
        <Text style={styles.questEmoji}>🎯</Text>
        <View style={styles.questInfo}>
          <Text style={styles.questTitle}>{quest.title}</Text>
          <Text style={styles.questDescription}>{quest.description}</Text>
        </View>
        <View style={styles.rewards}>
          <Text style={styles.rewardText}>🪙 {quest.reward_coins}</Text>
          <Text style={styles.rewardText}>⭐ {quest.reward_xp} XP</Text>
        </View>
      </View>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { width: `${Math.max(0, Math.min(100, progressPercentage))}%` }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>
          {progressPercentage}% Complete
        </Text>
      </View>
      
      <View style={styles.tapHint}>
        <Text style={styles.tapHintText}>Tap to view details</Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.success,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    ...Shadows.card,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  questEmoji: {
    fontSize: 24,
    marginRight: 12,
  },
  questInfo: {
    flex: 1,
    marginRight: 12,
  },
  questTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
  },
  questDescription: {
    fontSize: 14,
    color: Colors.text,
    opacity: 0.9,
    lineHeight: 18,
  },
  rewards: {
    alignItems: 'flex-end',
  },
  rewardText: {
    fontSize: 12,
    color: Colors.text,
    fontWeight: '600',
    marginBottom: 2,
  },
  progressContainer: {
    marginBottom: 8,
  },
  progressBar: {
    height: 6,
    backgroundColor: Colors.text,
    opacity: 0.3,
    borderRadius: 3,
    overflow: 'hidden',
    marginBottom: 4,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.text,
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    color: Colors.text,
    fontWeight: '600',
    textAlign: 'center',
  },
  tapHint: {
    alignItems: 'center',
  },
  tapHintText: {
    fontSize: 11,
    color: Colors.text,
    opacity: 0.7,
    fontStyle: 'italic',
  },
});
