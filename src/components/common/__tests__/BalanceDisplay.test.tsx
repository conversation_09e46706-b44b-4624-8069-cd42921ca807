import React from 'react';
import { render } from '@testing-library/react-native';
import { BalanceDisplay } from '../BalanceDisplay';

describe('BalanceDisplay', () => {
  it('should render coins correctly', () => {
    const { getByText } = render(
      <BalanceDisplay coins={1000} spins={5} gems={10} />
    );

    expect(getByText('1,000')).toBeTruthy();
  });

  it('should render spins correctly', () => {
    const { getByText } = render(
      <BalanceDisplay coins={1000} spins={5} gems={10} />
    );

    expect(getByText('5')).toBeTruthy();
  });

  it('should render gems correctly', () => {
    const { getByText } = render(
      <BalanceDisplay coins={1000} spins={5} gems={10} />
    );

    expect(getByText('10')).toBeTruthy();
  });

  it('should format large numbers correctly', () => {
    const { getByText } = render(
      <BalanceDisplay coins={1234567} spins={0} gems={0} />
    );

    expect(getByText('1,234,567')).toBeTruthy();
  });

  it('should handle zero values', () => {
    const { getByText } = render(
      <BalanceDisplay coins={0} spins={0} gems={0} />
    );

    expect(getByText('0')).toBeTruthy();
  });

  it('should render loading state when specified', () => {
    const { getByText } = render(
      <BalanceDisplay coins={1000} spins={5} gems={10} loading={true} />
    );

    expect(getByText('Loading...')).toBeTruthy();
  });
});
