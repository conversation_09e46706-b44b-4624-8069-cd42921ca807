import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Colors, Shadows } from '../../constants/colors';

interface GameTileProps {
  title: string;
  subtitle: string;
  emoji: string;
  onPress: () => void;
  locked?: boolean;
  isNew?: boolean;
  disabled?: boolean;
}

export const GameTile: React.FC<GameTileProps> = ({
  title,
  subtitle,
  emoji,
  onPress,
  locked = false,
  isNew = false,
  disabled = false,
}) => {
  const handlePress = () => {
    if (!locked && !disabled) {
      onPress();
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        locked && styles.lockedContainer,
        disabled && styles.disabledContainer,
      ]}
      onPress={handlePress}
      disabled={locked || disabled}
      activeOpacity={0.8}
    >
      {isNew && (
        <View style={styles.newBadge}>
          <Text style={styles.newBadgeText}>NEW</Text>
        </View>
      )}
      
      <View style={styles.content}>
        <Text style={[styles.emoji, locked && styles.lockedEmoji]}>
          {locked ? '🔒' : emoji}
        </Text>
        
        <Text style={[styles.title, locked && styles.lockedText]}>
          {title}
        </Text>
        
        <Text style={[styles.subtitle, locked && styles.lockedText]}>
          {locked ? 'Coming Soon' : subtitle}
        </Text>
      </View>
      
      {locked && (
        <View style={styles.lockOverlay}>
          <Text style={styles.lockText}>LOCKED</Text>
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 120,
    position: 'relative',
    ...Shadows.card,
  },
  lockedContainer: {
    opacity: 0.6,
    backgroundColor: Colors.disabled,
  },
  disabledContainer: {
    opacity: 0.5,
  },
  newBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: Colors.success,
    borderRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    zIndex: 1,
  },
  newBadgeText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.text,
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  emoji: {
    fontSize: 32,
    marginBottom: 8,
  },
  lockedEmoji: {
    opacity: 0.5,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 4,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  lockedText: {
    color: Colors.textSecondary,
    opacity: 0.7,
  },
  lockOverlay: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
    backgroundColor: Colors.error,
    borderRadius: 6,
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  lockText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: Colors.text,
    textAlign: 'center',
  },
});
