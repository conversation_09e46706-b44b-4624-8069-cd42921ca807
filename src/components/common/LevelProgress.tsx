import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Shadows } from '../../constants/colors';

interface LevelProgressProps {
  level: number;
  currentXP: number;
  nextLevelXP: number;
  progressToNext: number; // 0-1
  loading?: boolean;
}

export const LevelProgress: React.FC<LevelProgressProps> = ({
  level,
  currentXP,
  nextLevelXP,
  progressToNext,
  loading = false,
}) => {
  const xpToNext = nextLevelXP - currentXP;

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.levelText}>Level {level}</Text>
        <Text style={styles.xpText}>
          {currentXP.toLocaleString()} XP
        </Text>
      </View>
      
      <View style={styles.progressContainer}>
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { width: `${Math.max(0, Math.min(100, progressToNext * 100))}%` }
            ]} 
          />
        </View>
        <Text style={styles.progressText}>
          {loading ? 'Loading...' : `${xpToNext.toLocaleString()} XP to next level`}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    ...Shadows.card,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  levelText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  xpText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  progressContainer: {
    gap: 8,
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.background,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});
