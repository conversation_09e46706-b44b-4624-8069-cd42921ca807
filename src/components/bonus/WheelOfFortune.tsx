import React, { useEffect } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  Easing,
} from 'react-native-reanimated';
import Svg, {
  Circle,
  Path,
  Text as SvgText,
  G,
  Defs,
  LinearGradient,
  Stop,
} from 'react-native-svg';
import { Colors } from '../../constants/colors';

interface WheelOfFortuneProps {
  spinning: boolean;
  onSpinComplete?: () => void;
  winAmount?: number | null;
}

const { width } = Dimensions.get('window');
const WHEEL_SIZE = Math.min(width - 40, 300);
const RADIUS = WHEEL_SIZE / 2;
const CENTER = RADIUS;

// Wheel segments with different rewards
const SEGMENTS = [
  { label: '50', color: '#FF6B6B', coins: 50 },
  { label: '75', color: '#4ECDC4', coins: 75 },
  { label: '100', color: '#45B7D1', coins: 100 },
  { label: '125', color: '#96CEB4', coins: 125 },
  { label: '150', color: '#FFEAA7', coins: 150 },
  { label: '200', color: '#DDA0DD', coins: 200 },
  { label: '75', color: '#98D8C8', coins: 75 },
  { label: '100', color: '#F7DC6F', coins: 100 },
];

const SEGMENT_ANGLE = 360 / SEGMENTS.length;

export const WheelOfFortune: React.FC<WheelOfFortuneProps> = ({
  spinning,
  onSpinComplete,
  winAmount,
}) => {
  const rotation = useSharedValue(0);

  useEffect(() => {
    if (spinning) {
      // Calculate target rotation (multiple full rotations + random position)
      const randomSegment = Math.floor(Math.random() * SEGMENTS.length);
      const targetRotation = 360 * 5 + (randomSegment * SEGMENT_ANGLE) + Math.random() * SEGMENT_ANGLE;
      
      rotation.value = withSequence(
        withTiming(targetRotation, {
          duration: 3000,
          easing: Easing.out(Easing.cubic),
        }),
        withTiming(targetRotation + 10, { duration: 100 }),
        withTiming(targetRotation - 5, { duration: 100 }),
        withTiming(targetRotation, { duration: 100 })
      );

      // Call completion callback after animation
      setTimeout(() => {
        onSpinComplete?.();
      }, 3300);
    }
  }, [spinning]);

  const wheelAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ rotate: `${rotation.value}deg` }],
    };
  });

  // Create SVG path for each segment
  const createSegmentPath = (index: number): string => {
    const startAngle = (index * SEGMENT_ANGLE - 90) * (Math.PI / 180);
    const endAngle = ((index + 1) * SEGMENT_ANGLE - 90) * (Math.PI / 180);
    
    const x1 = CENTER + (RADIUS - 20) * Math.cos(startAngle);
    const y1 = CENTER + (RADIUS - 20) * Math.sin(startAngle);
    const x2 = CENTER + (RADIUS - 20) * Math.cos(endAngle);
    const y2 = CENTER + (RADIUS - 20) * Math.sin(endAngle);
    
    const largeArcFlag = SEGMENT_ANGLE > 180 ? 1 : 0;
    
    return [
      `M ${CENTER} ${CENTER}`,
      `L ${x1} ${y1}`,
      `A ${RADIUS - 20} ${RADIUS - 20} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ');
  };

  // Calculate text position for each segment
  const getTextPosition = (index: number) => {
    const angle = (index * SEGMENT_ANGLE + SEGMENT_ANGLE / 2 - 90) * (Math.PI / 180);
    const textRadius = RADIUS - 60;
    const x = CENTER + textRadius * Math.cos(angle);
    const y = CENTER + textRadius * Math.sin(angle);
    return { x, y };
  };

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.wheelContainer, wheelAnimatedStyle]}>
        <Svg width={WHEEL_SIZE} height={WHEEL_SIZE}>
          <Defs>
            {SEGMENTS.map((segment, index) => (
              <LinearGradient
                key={`gradient-${index}`}
                id={`gradient-${index}`}
                x1="0%"
                y1="0%"
                x2="100%"
                y2="100%"
              >
                <Stop offset="0%" stopColor={segment.color} stopOpacity="1" />
                <Stop offset="100%" stopColor={segment.color} stopOpacity="0.7" />
              </LinearGradient>
            ))}
          </Defs>
          
          {/* Wheel segments */}
          {SEGMENTS.map((segment, index) => {
            const textPos = getTextPosition(index);
            return (
              <G key={index}>
                <Path
                  d={createSegmentPath(index)}
                  fill={`url(#gradient-${index})`}
                  stroke={Colors.background}
                  strokeWidth="2"
                />
                <SvgText
                  x={textPos.x}
                  y={textPos.y}
                  fontSize="16"
                  fontWeight="bold"
                  fill={Colors.background}
                  textAnchor="middle"
                  alignmentBaseline="middle"
                >
                  {segment.label}
                </SvgText>
              </G>
            );
          })}
          
          {/* Center circle */}
          <Circle
            cx={CENTER}
            cy={CENTER}
            r="25"
            fill={Colors.primary}
            stroke={Colors.background}
            strokeWidth="3"
          />
          
          {/* Center text */}
          <SvgText
            x={CENTER}
            y={CENTER}
            fontSize="12"
            fontWeight="bold"
            fill={Colors.background}
            textAnchor="middle"
            alignmentBaseline="middle"
          >
            SPIN
          </SvgText>
        </Svg>
      </Animated.View>
      
      {/* Pointer */}
      <View style={styles.pointer}>
        <Svg width="30" height="40">
          <Path
            d="M15 0 L25 15 L15 10 L5 15 Z"
            fill={Colors.primary}
            stroke={Colors.background}
            strokeWidth="2"
          />
        </Svg>
      </View>
      
      {/* Win amount display */}
      {winAmount && (
        <View style={styles.winDisplay}>
          <SvgText
            x="50%"
            y="50%"
            fontSize="24"
            fontWeight="bold"
            fill={Colors.primary}
            textAnchor="middle"
            alignmentBaseline="middle"
          >
            +{winAmount}
          </SvgText>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  wheelContainer: {
    shadowColor: Colors.primary,
    shadowOpacity: 0.3,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 5 },
    elevation: 10,
  },
  pointer: {
    position: 'absolute',
    top: -5,
    zIndex: 10,
  },
  winDisplay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -50 }, { translateY: -50 }],
    backgroundColor: Colors.background,
    borderRadius: 25,
    width: 100,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.primary,
    zIndex: 15,
  },
});
