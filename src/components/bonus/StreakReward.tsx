import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Shadows } from '../../constants/colors';

interface StreakRewardProps {
  streak: number;
  nextStreakBonus: number;
}

export const StreakReward: React.FC<StreakRewardProps> = ({
  streak,
  nextStreakBonus,
}) => {
  const getStreakEmoji = (streakCount: number): string => {
    if (streakCount === 0) return '🌱';
    if (streakCount < 3) return '🔥';
    if (streakCount < 7) return '🚀';
    if (streakCount < 14) return '⭐';
    if (streakCount < 30) return '💎';
    return '👑';
  };

  const getStreakTitle = (streakCount: number): string => {
    if (streakCount === 0) return 'New Start';
    if (streakCount < 3) return 'Getting Started';
    if (streakCount < 7) return 'On Fire';
    if (streakCount < 14) return 'Superstar';
    if (streakCount < 30) return 'Diamond Player';
    return 'Legendary';
  };

  const getStreakBonus = (streakCount: number): number => {
    return Math.min(50 + (streakCount - 1) * 10, 200);
  };

  const getNextMilestone = (streakCount: number): { days: number; reward: string } => {
    if (streakCount < 3) return { days: 3, reward: 'Rocket Badge' };
    if (streakCount < 7) return { days: 7, reward: 'Star Badge' };
    if (streakCount < 14) return { days: 14, reward: 'Diamond Badge' };
    if (streakCount < 30) return { days: 30, reward: 'Crown Badge' };
    return { days: streakCount + 7, reward: 'Bonus Multiplier' };
  };

  const currentBonus = getStreakBonus(streak);
  const nextBonus = getStreakBonus(nextStreakBonus);
  const milestone = getNextMilestone(streak);

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.emoji}>{getStreakEmoji(streak)}</Text>
        <View style={styles.streakInfo}>
          <Text style={styles.streakCount}>{streak} Day Streak</Text>
          <Text style={styles.streakTitle}>{getStreakTitle(streak)}</Text>
        </View>
        <View style={styles.bonusInfo}>
          <Text style={styles.bonusAmount}>+{currentBonus}</Text>
          <Text style={styles.bonusLabel}>coins</Text>
        </View>
      </View>

      {/* Progress to next milestone */}
      <View style={styles.progressContainer}>
        <View style={styles.progressHeader}>
          <Text style={styles.progressLabel}>
            Next milestone: {milestone.days} days
          </Text>
          <Text style={styles.progressReward}>
            🏆 {milestone.reward}
          </Text>
        </View>
        
        <View style={styles.progressBar}>
          <View 
            style={[
              styles.progressFill, 
              { 
                width: `${Math.min(100, (streak / milestone.days) * 100)}%` 
              }
            ]} 
          />
        </View>
        
        <Text style={styles.progressText}>
          {milestone.days - streak} days to go
        </Text>
      </View>

      {/* Streak benefits */}
      <View style={styles.benefitsContainer}>
        <Text style={styles.benefitsTitle}>Streak Benefits</Text>
        <View style={styles.benefitsList}>
          <View style={styles.benefitItem}>
            <Text style={styles.benefitEmoji}>💰</Text>
            <Text style={styles.benefitText}>
              Current bonus: {currentBonus} coins
            </Text>
          </View>
          
          <View style={styles.benefitItem}>
            <Text style={styles.benefitEmoji}>📈</Text>
            <Text style={styles.benefitText}>
              Next day bonus: {nextBonus} coins
            </Text>
          </View>
          
          <View style={styles.benefitItem}>
            <Text style={styles.benefitEmoji}>🎯</Text>
            <Text style={styles.benefitText}>
              Max daily bonus: 200 coins
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    ...Shadows.card,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  emoji: {
    fontSize: 40,
    marginRight: 16,
  },
  streakInfo: {
    flex: 1,
  },
  streakCount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  streakTitle: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  bonusInfo: {
    alignItems: 'center',
  },
  bonusAmount: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.success,
  },
  bonusLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressLabel: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '600',
  },
  progressReward: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: '600',
  },
  progressBar: {
    height: 8,
    backgroundColor: Colors.background,
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 8,
  },
  progressFill: {
    height: '100%',
    backgroundColor: Colors.primary,
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  benefitsContainer: {
    borderTopWidth: 1,
    borderTopColor: Colors.background,
    paddingTop: 16,
  },
  benefitsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  benefitsList: {
    gap: 8,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  benefitEmoji: {
    fontSize: 16,
    marginRight: 12,
    width: 20,
  },
  benefitText: {
    fontSize: 14,
    color: Colors.textSecondary,
    flex: 1,
  },
});
