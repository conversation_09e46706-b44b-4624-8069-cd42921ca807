import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Shadows } from '../../constants/colors';

interface PrivacyConsentProps {
  onAccept: () => void;
  onDecline: () => void;
}

const PRIVACY_CONSENT_KEY = 'privacy_consent_accepted';

export const PrivacyConsent: React.FC<PrivacyConsentProps> = ({
  onAccept,
  onDecline,
}) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkConsentStatus();
  }, []);

  const checkConsentStatus = async () => {
    try {
      const consentStatus = await AsyncStorage.getItem(PRIVACY_CONSENT_KEY);
      if (!consentStatus) {
        setVisible(true);
      }
    } catch (error) {
      console.error('Error checking consent status:', error);
      setVisible(true); // Show consent on error to be safe
    } finally {
      setLoading(false);
    }
  };

  const handleAccept = async () => {
    try {
      await AsyncStorage.setItem(PRIVACY_CONSENT_KEY, 'true');
      setVisible(false);
      onAccept();
    } catch (error) {
      console.error('Error saving consent:', error);
    }
  };

  const handleDecline = async () => {
    try {
      await AsyncStorage.setItem(PRIVACY_CONSENT_KEY, 'false');
      setVisible(false);
      onDecline();
    } catch (error) {
      console.error('Error saving consent:', error);
    }
  };

  if (loading || !visible) {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={false}
      onRequestClose={() => {}} // Prevent dismissal
    >
      <SafeAreaView style={styles.container}>
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.title}>🔒 Privacy & Data</Text>
            <Text style={styles.subtitle}>
              Your privacy matters to us
            </Text>
          </View>

          <ScrollView style={styles.scrollContent} showsVerticalScrollIndicator={false}>
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>What We Collect</Text>
              <Text style={styles.text}>
                • Game progress and statistics (coins, level, achievements)
              </Text>
              <Text style={styles.text}>
                • Account information (email, username)
              </Text>
              <Text style={styles.text}>
                • Gameplay analytics (spins, wins, time played)
              </Text>
              <Text style={styles.text}>
                • Purchase history for in-app transactions
              </Text>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>How We Use Your Data</Text>
              <Text style={styles.text}>
                • Provide and improve our gaming experience
              </Text>
              <Text style={styles.text}>
                • Save your progress across devices
              </Text>
              <Text style={styles.text}>
                • Process in-app purchases securely
              </Text>
              <Text style={styles.text}>
                • Send you daily bonus notifications (if enabled)
              </Text>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Data Security</Text>
              <Text style={styles.text}>
                • All data is encrypted and stored securely with Supabase
              </Text>
              <Text style={styles.text}>
                • We never sell your personal information
              </Text>
              <Text style={styles.text}>
                • You can delete your account and data at any time
              </Text>
            </View>

            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Your Rights</Text>
              <Text style={styles.text}>
                • Access your data through the app settings
              </Text>
              <Text style={styles.text}>
                • Delete your account and all associated data
              </Text>
              <Text style={styles.text}>
                • Opt out of notifications at any time
              </Text>
              <Text style={styles.text}>
                • Contact us for data-related requests
              </Text>
            </View>

            <View style={styles.legalSection}>
              <Text style={styles.legalText}>
                By continuing, you agree to our Privacy Policy and Terms of Service. 
                You must be 18+ to play. This app contains simulated gambling and is for entertainment only.
              </Text>
            </View>
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.declineButton}
              onPress={handleDecline}
            >
              <Text style={styles.declineButtonText}>Decline</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.acceptButton}
              onPress={handleAccept}
            >
              <Text style={styles.acceptButtonText}>Accept & Continue</Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  scrollContent: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  text: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
    marginBottom: 8,
  },
  legalSection: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
  },
  legalText: {
    fontSize: 12,
    color: Colors.textSecondary,
    lineHeight: 16,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  declineButton: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.error,
  },
  declineButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.error,
  },
  acceptButton: {
    flex: 2,
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    ...Shadows.button,
  },
  acceptButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.background,
  },
});
