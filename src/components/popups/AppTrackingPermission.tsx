import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Platform,
  SafeAreaView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Colors, Shadows } from '../../constants/colors';

interface AppTrackingPermissionProps {
  onAllow: () => void;
  onDeny: () => void;
}

const TRACKING_PERMISSION_KEY = 'app_tracking_permission_requested';

export const AppTrackingPermission: React.FC<AppTrackingPermissionProps> = ({
  onAllow,
  onDeny,
}) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkPermissionStatus();
  }, []);

  const checkPermissionStatus = async () => {
    try {
      // Only show on iOS 14.5+ where ATT is required
      if (Platform.OS !== 'ios') {
        setLoading(false);
        return;
      }

      const permissionRequested = await AsyncStorage.getItem(TRACKING_PERMISSION_KEY);
      if (!permissionRequested) {
        // Small delay to show after privacy consent
        setTimeout(() => {
          setVisible(true);
          setLoading(false);
        }, 1000);
      } else {
        setLoading(false);
      }
    } catch (error) {
      console.error('Error checking tracking permission status:', error);
      setLoading(false);
    }
  };

  const handleAllow = async () => {
    try {
      // In a real app, you would call:
      // import { requestTrackingPermissionsAsync } from 'expo-tracking-transparency';
      // const { status } = await requestTrackingPermissionsAsync();
      
      await AsyncStorage.setItem(TRACKING_PERMISSION_KEY, 'requested');
      setVisible(false);
      onAllow();
    } catch (error) {
      console.error('Error requesting tracking permission:', error);
    }
  };

  const handleDeny = async () => {
    try {
      await AsyncStorage.setItem(TRACKING_PERMISSION_KEY, 'requested');
      setVisible(false);
      onDeny();
    } catch (error) {
      console.error('Error saving tracking permission:', error);
    }
  };

  if (loading || !visible || Platform.OS !== 'ios') {
    return null;
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={() => {}} // Prevent dismissal
    >
      <View style={styles.overlay}>
        <SafeAreaView style={styles.container}>
          <View style={styles.content}>
            <View style={styles.header}>
              <Text style={styles.icon}>🎯</Text>
              <Text style={styles.title}>Personalized Experience</Text>
              <Text style={styles.subtitle}>
                Help us provide you with a better gaming experience
              </Text>
            </View>

            <View style={styles.benefitsContainer}>
              <View style={styles.benefit}>
                <Text style={styles.benefitIcon}>🎮</Text>
                <View style={styles.benefitText}>
                  <Text style={styles.benefitTitle}>Better Game Recommendations</Text>
                  <Text style={styles.benefitDescription}>
                    Get personalized game suggestions based on your preferences
                  </Text>
                </View>
              </View>

              <View style={styles.benefit}>
                <Text style={styles.benefitIcon}>🎁</Text>
                <View style={styles.benefitText}>
                  <Text style={styles.benefitTitle}>Tailored Offers</Text>
                  <Text style={styles.benefitDescription}>
                    Receive special offers and bonuses that match your play style
                  </Text>
                </View>
              </View>

              <View style={styles.benefit}>
                <Text style={styles.benefitIcon}>📊</Text>
                <View style={styles.benefitText}>
                  <Text style={styles.benefitTitle}>Improved Features</Text>
                  <Text style={styles.benefitDescription}>
                    Help us understand what features you love most
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.privacyNote}>
              <Text style={styles.privacyText}>
                🔒 Your privacy is important to us. This data helps us improve your experience 
                while keeping your information secure. You can change this setting anytime in your device settings.
              </Text>
            </View>

            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={styles.denyButton}
                onPress={handleDeny}
              >
                <Text style={styles.denyButtonText}>Ask App Not to Track</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.allowButton}
                onPress={handleAllow}
              >
                <Text style={styles.allowButtonText}>Allow</Text>
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: '90%',
    maxWidth: 400,
  },
  content: {
    backgroundColor: Colors.background,
    borderRadius: 16,
    padding: 24,
    ...Shadows.card,
  },
  header: {
    alignItems: 'center',
    marginBottom: 24,
  },
  icon: {
    fontSize: 48,
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  benefitsContainer: {
    marginBottom: 24,
  },
  benefit: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  benefitIcon: {
    fontSize: 24,
    marginRight: 12,
    marginTop: 2,
  },
  benefitText: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  benefitDescription: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 18,
  },
  privacyNote: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  privacyText: {
    fontSize: 12,
    color: Colors.textSecondary,
    lineHeight: 16,
    textAlign: 'center',
  },
  buttonContainer: {
    gap: 12,
  },
  denyButton: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.textSecondary,
  },
  denyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.textSecondary,
  },
  allowButton: {
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    ...Shadows.button,
  },
  allowButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.background,
  },
});
