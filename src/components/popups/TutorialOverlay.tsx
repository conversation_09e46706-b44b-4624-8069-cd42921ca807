import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  SafeAreaView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  withSequence,
} from 'react-native-reanimated';
import { Colors, Shadows } from '../../constants/colors';

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  targetArea?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  position: 'top' | 'bottom' | 'center';
}

interface TutorialOverlayProps {
  steps: TutorialStep[];
  onComplete: () => void;
  onSkip: () => void;
}

const TUTORIAL_COMPLETED_KEY = 'tutorial_completed';
const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export const TutorialOverlay: React.FC<TutorialOverlayProps> = ({
  steps,
  onComplete,
  onSkip,
}) => {
  const [visible, setVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(true);

  const pulseScale = useSharedValue(1);
  const spotlightOpacity = useSharedValue(0);

  useEffect(() => {
    checkTutorialStatus();
  }, []);

  useEffect(() => {
    if (visible) {
      // Animate spotlight
      spotlightOpacity.value = withTiming(1, { duration: 500 });
      
      // Pulse animation for highlighted area
      pulseScale.value = withRepeat(
        withSequence(
          withTiming(1.1, { duration: 800 }),
          withTiming(1, { duration: 800 })
        ),
        -1,
        true
      );
    }
  }, [visible, currentStep]);

  const checkTutorialStatus = async () => {
    try {
      const tutorialCompleted = await AsyncStorage.getItem(TUTORIAL_COMPLETED_KEY);
      if (!tutorialCompleted && steps.length > 0) {
        setVisible(true);
      }
    } catch (error) {
      console.error('Error checking tutorial status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = async () => {
    try {
      await AsyncStorage.setItem(TUTORIAL_COMPLETED_KEY, 'true');
      setVisible(false);
      onComplete();
    } catch (error) {
      console.error('Error saving tutorial completion:', error);
    }
  };

  const handleSkip = async () => {
    try {
      await AsyncStorage.setItem(TUTORIAL_COMPLETED_KEY, 'true');
      setVisible(false);
      onSkip();
    } catch (error) {
      console.error('Error saving tutorial skip:', error);
    }
  };

  const spotlightAnimatedStyle = useAnimatedStyle(() => {
    return {
      opacity: spotlightOpacity.value,
    };
  });

  const pulseAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: pulseScale.value }],
    };
  });

  if (loading || !visible || steps.length === 0) {
    return null;
  }

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;

  return (
    <Modal
      visible={visible}
      animationType="fade"
      transparent={true}
      onRequestClose={() => {}} // Prevent dismissal
    >
      <View style={styles.overlay}>
        {/* Spotlight effect */}
        <Animated.View style={[styles.spotlight, spotlightAnimatedStyle]}>
          {currentStepData.targetArea && (
            <Animated.View
              style={[
                styles.highlightArea,
                {
                  left: currentStepData.targetArea.x,
                  top: currentStepData.targetArea.y,
                  width: currentStepData.targetArea.width,
                  height: currentStepData.targetArea.height,
                },
                pulseAnimatedStyle,
              ]}
            />
          )}
        </Animated.View>

        <SafeAreaView style={styles.container}>
          {/* Tutorial Content */}
          <View
            style={[
              styles.content,
              currentStepData.position === 'top' && styles.contentTop,
              currentStepData.position === 'bottom' && styles.contentBottom,
              currentStepData.position === 'center' && styles.contentCenter,
            ]}
          >
            <View style={styles.card}>
              <View style={styles.header}>
                <Text style={styles.stepIndicator}>
                  {currentStep + 1} of {steps.length}
                </Text>
                <TouchableOpacity onPress={handleSkip}>
                  <Text style={styles.skipText}>Skip</Text>
                </TouchableOpacity>
              </View>

              <Text style={styles.title}>{currentStepData.title}</Text>
              <Text style={styles.description}>{currentStepData.description}</Text>

              <View style={styles.buttonContainer}>
                {currentStep > 0 && (
                  <TouchableOpacity
                    style={styles.previousButton}
                    onPress={handlePrevious}
                  >
                    <Text style={styles.previousButtonText}>Previous</Text>
                  </TouchableOpacity>
                )}

                <TouchableOpacity
                  style={[styles.nextButton, currentStep === 0 && styles.nextButtonFull]}
                  onPress={handleNext}
                >
                  <Text style={styles.nextButtonText}>
                    {isLastStep ? 'Get Started!' : 'Next'}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Progress dots */}
              <View style={styles.progressContainer}>
                {steps.map((_, index) => (
                  <View
                    key={index}
                    style={[
                      styles.progressDot,
                      index === currentStep && styles.progressDotActive,
                    ]}
                  />
                ))}
              </View>
            </View>
          </View>
        </SafeAreaView>
      </View>
    </Modal>
  );
};

// Default tutorial steps for the casino app
export const defaultTutorialSteps: TutorialStep[] = [
  {
    id: 'welcome',
    title: '🎰 Welcome to Jackpot Party!',
    description: 'Get ready for the ultimate casino experience! Let\'s show you around.',
    position: 'center',
  },
  {
    id: 'balance',
    title: '💰 Your Balance',
    description: 'Keep track of your coins, gems, and free spins here. You start with 1000 welcome coins!',
    position: 'top',
  },
  {
    id: 'daily_bonus',
    title: '🎁 Daily Bonus',
    description: 'Don\'t forget to claim your daily bonus! Build up your streak for bigger rewards.',
    position: 'top',
  },
  {
    id: 'slot_games',
    title: '🎮 Slot Games',
    description: 'Choose from different slot machines. Each has unique features and payout rates!',
    position: 'center',
  },
  {
    id: 'shop',
    title: '🛒 Coin Shop',
    description: 'Running low on coins? Visit the shop to purchase more and keep the fun going!',
    position: 'bottom',
  },
  {
    id: 'ready',
    title: '🚀 You\'re Ready!',
    description: 'That\'s it! You\'re all set to start playing. Good luck and have fun!',
    position: 'center',
  },
];

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  spotlight: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  highlightArea: {
    position: 'absolute',
    borderRadius: 12,
    borderWidth: 3,
    borderColor: Colors.primary,
    backgroundColor: 'transparent',
  },
  container: {
    flex: 1,
  },
  content: {
    position: 'absolute',
    left: 20,
    right: 20,
  },
  contentTop: {
    top: 100,
  },
  contentBottom: {
    bottom: 100,
  },
  contentCenter: {
    top: '50%',
    transform: [{ translateY: -150 }],
  },
  card: {
    backgroundColor: Colors.background,
    borderRadius: 16,
    padding: 24,
    ...Shadows.card,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepIndicator: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '600',
  },
  skipText: {
    fontSize: 14,
    color: Colors.textSecondary,
    fontWeight: '600',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: Colors.textSecondary,
    lineHeight: 22,
    textAlign: 'center',
    marginBottom: 24,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  previousButton: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  previousButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary,
  },
  nextButton: {
    flex: 1,
    backgroundColor: Colors.primary,
    borderRadius: 12,
    paddingVertical: 14,
    alignItems: 'center',
    ...Shadows.button,
  },
  nextButtonFull: {
    flex: 2,
  },
  nextButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.background,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.disabled,
  },
  progressDotActive: {
    backgroundColor: Colors.primary,
  },
});
