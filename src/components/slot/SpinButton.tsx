import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { Colors, Shadows } from '../../constants/colors';

interface SpinButtonProps {
  onPress: () => void;
  disabled: boolean;
  spinning: boolean;
}

export const SpinButton: React.FC<SpinButtonProps> = ({ onPress, disabled, spinning }) => {
  return (
    <TouchableOpacity
      style={[styles.button, disabled && styles.disabled]}
      onPress={onPress}
      disabled={disabled}
    >
      {spinning ? (
        <ActivityIndicator size="small" color={Colors.background} />
      ) : (
        <Text style={styles.text}>SPIN</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 40,
    paddingVertical: 16,
    borderRadius: 32,
    marginTop: 16,
    ...Shadows.button,
  },
  text: {
    color: Colors.background,
    fontSize: 22,
    fontWeight: 'bold',
    letterSpacing: 2,
  },
  disabled: {
    opacity: 0.5,
  },
});