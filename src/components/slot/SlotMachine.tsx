import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors, Shadows } from '../../constants/colors';

interface SlotMachineProps {
  reels: string[][];
}

export const SlotMachine: React.FC<SlotMachineProps> = ({ reels }) => {
  return (
    <View style={styles.container}>
      {reels.map((reel, i) => (
        <View key={i} style={styles.reel}>
          {reel.map((symbol, j) => (
            <Text key={j} style={styles.symbol}>{symbol}</Text>
          ))}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginBottom: 32,
    backgroundColor: Colors.card,
    borderRadius: 16,
    padding: 12,
    ...Shadows.card,
  },
  reel: {
    marginHorizontal: 6,
    alignItems: 'center',
  },
  symbol: {
    fontSize: 36,
    marginVertical: 2,
  },
});