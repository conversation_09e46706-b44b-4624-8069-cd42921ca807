import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSequence,
  withDelay,
  Easing,
  runOnJS,
} from 'react-native-reanimated';
import { Colors } from '../../constants/colors';

interface AnimatedReelProps {
  symbols: string[];
  spinning: boolean;
  delay?: number;
  winningLines: number[];
  reelIndex: number;
}

// Extended symbol list for spinning animation
const EXTENDED_SYMBOLS = ['🍒', '🍋', '🔔', '7️⃣', '💎', '🎰', '🍒', '🍋', '🔔'];

export const AnimatedReel: React.FC<AnimatedReelProps> = ({
  symbols,
  spinning,
  delay = 0,
  winningLines,
  reelIndex,
}) => {
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  // Check if this reel has winning symbols
  const hasWinningSymbol = winningLines.length > 0;

  useEffect(() => {
    if (spinning) {
      // Start spinning animation
      translateY.value = withDelay(
        delay,
        withSequence(
          // Fast spinning phase
          withTiming(-300, {
            duration: 800,
            easing: Easing.out(Easing.quad),
          }),
          // Slow down and settle
          withTiming(0, {
            duration: 400,
            easing: Easing.bezier(0.25, 0.46, 0.45, 0.94),
          })
        )
      );

      // Add some bounce effect
      scale.value = withDelay(
        delay,
        withSequence(
          withTiming(0.95, { duration: 200 }),
          withTiming(1.05, { duration: 200 }),
          withTiming(1, { duration: 200 })
        )
      );
    }
  }, [spinning, delay]);

  // Winning animation effect
  useEffect(() => {
    if (hasWinningSymbol && !spinning) {
      // Highlight winning symbols
      scale.value = withSequence(
        withTiming(1.1, { duration: 300 }),
        withTiming(1, { duration: 300 }),
        withTiming(1.1, { duration: 300 }),
        withTiming(1, { duration: 300 })
      );
      
      opacity.value = withSequence(
        withTiming(0.8, { duration: 200 }),
        withTiming(1, { duration: 200 }),
        withTiming(0.8, { duration: 200 }),
        withTiming(1, { duration: 200 })
      );
    }
  }, [hasWinningSymbol, spinning]);

  const reelAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        { translateY: translateY.value },
        { scale: scale.value }
      ],
      opacity: opacity.value,
    };
  });

  const symbolAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  return (
    <View style={styles.reelContainer}>
      <Animated.View style={[styles.reel, reelAnimatedStyle]}>
        {spinning ? (
          // Show extended symbols during spinning
          <View style={styles.spinningSymbols}>
            {EXTENDED_SYMBOLS.map((symbol, index) => (
              <Animated.View key={index} style={[styles.symbolContainer, symbolAnimatedStyle]}>
                <Text style={styles.symbol}>{symbol}</Text>
              </Animated.View>
            ))}
          </View>
        ) : (
          // Show final symbols when not spinning
          <View style={styles.finalSymbols}>
            {symbols.map((symbol, index) => (
              <Animated.View 
                key={index} 
                style={[
                  styles.symbolContainer,
                  hasWinningSymbol && styles.winningSymbolContainer,
                  symbolAnimatedStyle
                ]}
              >
                <Text 
                  style={[
                    styles.symbol,
                    hasWinningSymbol && styles.winningSymbol
                  ]}
                >
                  {symbol}
                </Text>
              </Animated.View>
            ))}
          </View>
        )}
      </Animated.View>
      
      {/* Reel frame/border */}
      <View style={styles.reelFrame} />
    </View>
  );
};

const styles = StyleSheet.create({
  reelContainer: {
    width: 60,
    height: 180,
    marginHorizontal: 4,
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 8,
    backgroundColor: Colors.background,
  },
  reel: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  spinningSymbols: {
    position: 'absolute',
    top: -200,
    left: 0,
    right: 0,
    height: 400,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  finalSymbols: {
    flex: 1,
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingVertical: 8,
  },
  symbolContainer: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    backgroundColor: 'transparent',
  },
  winningSymbolContainer: {
    backgroundColor: Colors.primary,
    shadowColor: Colors.primary,
    shadowOpacity: 0.5,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 0 },
    elevation: 8,
  },
  symbol: {
    fontSize: 32,
    textAlign: 'center',
  },
  winningSymbol: {
    textShadowColor: Colors.background,
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  reelFrame: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderWidth: 2,
    borderColor: Colors.primary,
    borderRadius: 8,
    pointerEvents: 'none',
  },
});
