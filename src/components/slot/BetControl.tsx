import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import { Colors, Shadows } from '../../constants/colors';

interface BetControlProps {
  betAmount: number;
  onBetChange: (amount: number) => void;
  maxBet: number;
  disabled?: boolean;
}

const BET_PRESETS = [10, 25, 50, 100];

export const BetControl: React.FC<BetControlProps> = ({
  betAmount,
  onBetChange,
  maxBet,
  disabled = false,
}) => {
  const handleDecrease = () => {
    if (disabled) return;
    const newAmount = Math.max(10, betAmount - 10);
    onBetChange(newAmount);
  };

  const handleIncrease = () => {
    if (disabled) return;
    const newAmount = Math.min(maxBet, betAmount + 10);
    onBetChange(newAmount);
  };

  const handlePresetBet = (amount: number) => {
    if (disabled) return;
    const validAmount = Math.min(amount, maxBet);
    onBetChange(validAmount);
  };

  const handleMaxBet = () => {
    if (disabled) return;
    onBetChange(maxBet);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.label}>Bet Amount</Text>
      
      {/* Bet Amount Display and Controls */}
      <View style={styles.betControls}>
        <TouchableOpacity
          style={[
            styles.controlButton,
            (disabled || betAmount <= 10) && styles.controlButtonDisabled
          ]}
          onPress={handleDecrease}
          disabled={disabled || betAmount <= 10}
        >
          <Text style={[
            styles.controlButtonText,
            (disabled || betAmount <= 10) && styles.controlButtonTextDisabled
          ]}>
            −
          </Text>
        </TouchableOpacity>
        
        <View style={styles.betAmountContainer}>
          <Text style={styles.betAmount}>{betAmount}</Text>
          <Text style={styles.betAmountLabel}>coins</Text>
        </View>
        
        <TouchableOpacity
          style={[
            styles.controlButton,
            (disabled || betAmount >= maxBet) && styles.controlButtonDisabled
          ]}
          onPress={handleIncrease}
          disabled={disabled || betAmount >= maxBet}
        >
          <Text style={[
            styles.controlButtonText,
            (disabled || betAmount >= maxBet) && styles.controlButtonTextDisabled
          ]}>
            +
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Preset Bet Buttons */}
      <View style={styles.presetContainer}>
        {BET_PRESETS.map((preset) => (
          <TouchableOpacity
            key={preset}
            style={[
              styles.presetButton,
              betAmount === preset && styles.presetButtonActive,
              (disabled || preset > maxBet) && styles.presetButtonDisabled
            ]}
            onPress={() => handlePresetBet(preset)}
            disabled={disabled || preset > maxBet}
          >
            <Text style={[
              styles.presetButtonText,
              betAmount === preset && styles.presetButtonTextActive,
              (disabled || preset > maxBet) && styles.presetButtonTextDisabled
            ]}>
              {preset}
            </Text>
          </TouchableOpacity>
        ))}
        
        <TouchableOpacity
          style={[
            styles.maxButton,
            disabled && styles.maxButtonDisabled
          ]}
          onPress={handleMaxBet}
          disabled={disabled}
        >
          <Text style={[
            styles.maxButtonText,
            disabled && styles.maxButtonTextDisabled
          ]}>
            MAX
          </Text>
        </TouchableOpacity>
      </View>
      
      {maxBet < 100 && (
        <Text style={styles.warningText}>
          ⚠️ Insufficient coins for higher bets
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    ...Shadows.card,
  },
  label: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  betControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...Shadows.button,
  },
  controlButtonDisabled: {
    backgroundColor: Colors.disabled,
    opacity: 0.5,
  },
  controlButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.background,
  },
  controlButtonTextDisabled: {
    color: Colors.textSecondary,
  },
  betAmountContainer: {
    alignItems: 'center',
    marginHorizontal: 24,
    minWidth: 80,
  },
  betAmount: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.primary,
  },
  betAmountLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  presetContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
  },
  presetButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.textSecondary,
    alignItems: 'center',
  },
  presetButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  presetButtonDisabled: {
    opacity: 0.5,
    backgroundColor: Colors.disabled,
  },
  presetButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
  },
  presetButtonTextActive: {
    color: Colors.background,
  },
  presetButtonTextDisabled: {
    color: Colors.textSecondary,
  },
  maxButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: Colors.success,
    alignItems: 'center',
  },
  maxButtonDisabled: {
    backgroundColor: Colors.disabled,
    opacity: 0.5,
  },
  maxButtonText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.text,
  },
  maxButtonTextDisabled: {
    color: Colors.textSecondary,
  },
  warningText: {
    fontSize: 12,
    color: Colors.warning,
    textAlign: 'center',
    marginTop: 8,
  },
});
