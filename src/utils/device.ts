import * as Device from 'expo-device';
import * as SecureStore from 'expo-secure-store';

const DEVICE_ID_KEY = 'device_id';

export async function getDeviceId(): Promise<string> {
  try {
    // Try to get existing device ID from secure storage
    let deviceId = await SecureStore.getItemAsync(DEVICE_ID_KEY);
    
    if (!deviceId) {
      // Generate new device ID
      deviceId = generateDeviceId();
      await SecureStore.setItemAsync(DEVICE_ID_KEY, deviceId);
    }
    
    return deviceId;
  } catch (error) {
    // Fallback to in-memory device ID if secure storage fails
    console.warn('SecureStore not available, using temporary device ID');
    return generateDeviceId();
  }
}

function generateDeviceId(): string {
  const prefix = Device.isDevice ? 'device' : 'dev';
  const randomId = Math.random().toString(36).slice(2, 12);
  return `${prefix}-${randomId}`;
}