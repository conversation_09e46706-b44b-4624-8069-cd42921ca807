import 'react-native-url-polyfill/auto';
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Constants from 'expo-constants';

// Get configuration from Expo Constants
const supabaseUrl = Constants.expoConfig?.extra?.supabaseUrl;
const supabaseAnonKey = Constants.expoConfig?.extra?.supabaseAnonKey;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing Supabase configuration. Please check your .env file and app.config.js'
  );
}

// Create Supabase client with AsyncStorage for session persistence
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types for better TypeScript support
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          username: string | null;
          avatar_url: string | null;
          coins: number;
          spins: number;
          gems: number;
          xp: number;
          level: number;
          daily_streak: number;
          last_daily_bonus: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          username?: string | null;
          avatar_url?: string | null;
          coins?: number;
          spins?: number;
          gems?: number;
          xp?: number;
          level?: number;
          daily_streak?: number;
          last_daily_bonus?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          username?: string | null;
          avatar_url?: string | null;
          coins?: number;
          spins?: number;
          gems?: number;
          xp?: number;
          level?: number;
          daily_streak?: number;
          last_daily_bonus?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      game_sessions: {
        Row: {
          id: string;
          user_id: string;
          game_type: string;
          bet_amount: number;
          payout: number;
          result_data: any;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          game_type: string;
          bet_amount: number;
          payout: number;
          result_data?: any;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          game_type?: string;
          bet_amount?: number;
          payout?: number;
          result_data?: any;
          created_at?: string;
        };
      };
      quests: {
        Row: {
          id: string;
          user_id: string;
          quest_type: string;
          title: string;
          description: string;
          target_value: number;
          current_value: number;
          reward_coins: number;
          reward_xp: number;
          completed: boolean;
          expires_at: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          quest_type: string;
          title: string;
          description: string;
          target_value: number;
          current_value?: number;
          reward_coins: number;
          reward_xp: number;
          completed?: boolean;
          expires_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          quest_type?: string;
          title?: string;
          description?: string;
          target_value?: number;
          current_value?: number;
          reward_coins?: number;
          reward_xp?: number;
          completed?: boolean;
          expires_at?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      purchases: {
        Row: {
          id: string;
          user_id: string;
          product_id: string;
          transaction_id: string;
          amount: number;
          currency: string;
          coins_received: number;
          gems_received: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          product_id: string;
          transaction_id: string;
          amount: number;
          currency: string;
          coins_received: number;
          gems_received: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          product_id?: string;
          transaction_id?: string;
          amount?: number;
          currency?: string;
          coins_received?: number;
          gems_received?: number;
          created_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];
