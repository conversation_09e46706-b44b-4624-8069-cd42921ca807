export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`;
  } else if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
}

export function formatCurrency(amount: number, currency: 'coins' | 'gems' = 'coins'): string {
  const symbol = currency === 'gems' ? '💎' : '🪙';
  return `${symbol} ${formatNumber(amount)}`;
}