-- Jackpot Party Casino Database Schema
-- Run this in your Supabase SQL Editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- Create users table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT NOT NULL,
    username TEXT UNIQUE,
    avatar_url TEXT,
    push_token TEXT,
    coins BIGINT DEFAULT 1000 NOT NULL CHECK (coins >= 0),
    spins INTEGER DEFAULT 10 NOT NULL CHECK (spins >= 0),
    gems INTEGER DEFAULT 0 NOT NULL CHECK (gems >= 0),
    xp BIGINT DEFAULT 0 NOT NULL CHECK (xp >= 0),
    level INTEGER DEFAULT 1 NOT NULL CHECK (level >= 1),
    daily_streak INTEGER DEFAULT 0 NOT NULL CHECK (daily_streak >= 0),
    last_daily_bonus TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create game_sessions table for tracking spins and results
CREATE TABLE IF NOT EXISTS public.game_sessions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    game_type TEXT NOT NULL CHECK (game_type IN ('classic', 'mega', 'progressive', 'bonus')),
    bet_amount INTEGER NOT NULL CHECK (bet_amount > 0),
    payout INTEGER DEFAULT 0 NOT NULL CHECK (payout >= 0),
    result_data JSONB NOT NULL, -- Store reel results, winning lines, etc.
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create quests table for daily/weekly challenges
CREATE TABLE IF NOT EXISTS public.quests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    quest_type TEXT NOT NULL CHECK (quest_type IN ('daily', 'weekly', 'achievement')),
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    target_value INTEGER NOT NULL CHECK (target_value > 0),
    current_value INTEGER DEFAULT 0 NOT NULL CHECK (current_value >= 0),
    reward_coins INTEGER DEFAULT 0 NOT NULL CHECK (reward_coins >= 0),
    reward_xp INTEGER DEFAULT 0 NOT NULL CHECK (reward_xp >= 0),
    reward_gems INTEGER DEFAULT 0 NOT NULL CHECK (reward_gems >= 0),
    completed BOOLEAN DEFAULT FALSE NOT NULL,
    expires_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create purchases table for in-app purchases
CREATE TABLE IF NOT EXISTS public.purchases (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    product_id TEXT NOT NULL,
    transaction_id TEXT UNIQUE NOT NULL,
    amount DECIMAL(10,2) NOT NULL CHECK (amount > 0),
    currency TEXT DEFAULT 'USD' NOT NULL,
    coins_received INTEGER DEFAULT 0 NOT NULL CHECK (coins_received >= 0),
    gems_received INTEGER DEFAULT 0 NOT NULL CHECK (gems_received >= 0),
    spins_received INTEGER DEFAULT 0 NOT NULL CHECK (spins_received >= 0),
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create leaderboards table for competitive features
CREATE TABLE IF NOT EXISTS public.leaderboards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    leaderboard_type TEXT NOT NULL CHECK (leaderboard_type IN ('weekly_coins', 'monthly_coins', 'all_time_coins', 'level')),
    score BIGINT NOT NULL CHECK (score >= 0),
    rank INTEGER,
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, leaderboard_type, period_start)
);

-- Create analytics_events table for Supabase-only analytics
CREATE TABLE IF NOT EXISTS public.analytics_events (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    event_type TEXT NOT NULL CHECK (event_type IN ('spin', 'win', 'purchase', 'level_up', 'daily_collect', 'app_open', 'session_start', 'quest_complete')),
    event_data JSONB NOT NULL DEFAULT '{}',
    session_id TEXT NOT NULL,
    timestamp TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    app_version TEXT NOT NULL,
    platform TEXT NOT NULL,
    device_info JSONB NOT NULL DEFAULT '{}'
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON public.users(username);
CREATE INDEX IF NOT EXISTS idx_game_sessions_user_id ON public.game_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_game_sessions_created_at ON public.game_sessions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_quests_user_id ON public.quests(user_id);
CREATE INDEX IF NOT EXISTS idx_quests_completed ON public.quests(completed);
CREATE INDEX IF NOT EXISTS idx_purchases_user_id ON public.purchases(user_id);
CREATE INDEX IF NOT EXISTS idx_leaderboards_type_period ON public.leaderboards(leaderboard_type, period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_analytics_user_id ON public.analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_event_type ON public.analytics_events(event_type);
CREATE INDEX IF NOT EXISTS idx_analytics_timestamp ON public.analytics_events(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_analytics_session_id ON public.analytics_events(session_id);

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.game_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.quests ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.leaderboards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_events ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
CREATE POLICY "Users can view own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for game_sessions table
CREATE POLICY "Users can view own game sessions" ON public.game_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own game sessions" ON public.game_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for quests table
CREATE POLICY "Users can view own quests" ON public.quests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own quests" ON public.quests
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own quests" ON public.quests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for purchases table
CREATE POLICY "Users can view own purchases" ON public.purchases
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own purchases" ON public.purchases
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- RLS Policies for leaderboards table (read-only for users)
CREATE POLICY "Users can view leaderboards" ON public.leaderboards
    FOR SELECT USING (true);

-- RLS Policies for analytics_events table
CREATE POLICY "Users can view own analytics events" ON public.analytics_events
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own analytics events" ON public.analytics_events
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER handle_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_quests_updated_at
    BEFORE UPDATE ON public.quests
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

CREATE TRIGGER handle_leaderboards_updated_at
    BEFORE UPDATE ON public.leaderboards
    FOR EACH ROW EXECUTE FUNCTION public.handle_updated_at();

-- Create function to calculate user level from XP
CREATE OR REPLACE FUNCTION public.calculate_level(xp_amount BIGINT)
RETURNS INTEGER AS $$
BEGIN
    -- Level formula: level = floor(sqrt(xp / 100)) + 1
    -- This means: Level 1: 0-99 XP, Level 2: 100-399 XP, Level 3: 400-899 XP, etc.
    RETURN FLOOR(SQRT(xp_amount / 100.0)) + 1;
END;
$$ LANGUAGE plpgsql;

-- Create function to get XP required for next level
CREATE OR REPLACE FUNCTION public.xp_for_level(target_level INTEGER)
RETURNS BIGINT AS $$
BEGIN
    -- XP required for level = (level - 1)^2 * 100
    RETURN POWER(target_level - 1, 2) * 100;
END;
$$ LANGUAGE plpgsql;

-- Create function to update user level when XP changes
CREATE OR REPLACE FUNCTION public.update_user_level()
RETURNS TRIGGER AS $$
BEGIN
    NEW.level = public.calculate_level(NEW.xp);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-update level when XP changes
CREATE TRIGGER update_level_on_xp_change
    BEFORE UPDATE OF xp ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_user_level();

-- Create function for daily bonus logic
CREATE OR REPLACE FUNCTION public.claim_daily_bonus(user_uuid UUID)
RETURNS TABLE(
    new_coins BIGINT,
    new_streak INTEGER,
    bonus_amount INTEGER,
    next_bonus_at TIMESTAMPTZ
) AS $$
DECLARE
    current_user RECORD;
    bonus_coins INTEGER;
    new_streak_count INTEGER;
    last_bonus_date DATE;
    today_date DATE;
BEGIN
    -- Get current user data
    SELECT * INTO current_user FROM public.users WHERE id = user_uuid;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found';
    END IF;

    -- Check if user already claimed today
    today_date := CURRENT_DATE;
    last_bonus_date := DATE(current_user.last_daily_bonus);

    IF last_bonus_date = today_date THEN
        RAISE EXCEPTION 'Daily bonus already claimed today';
    END IF;

    -- Calculate streak
    IF last_bonus_date = today_date - INTERVAL '1 day' THEN
        -- Consecutive day, increment streak
        new_streak_count := current_user.daily_streak + 1;
    ELSE
        -- Streak broken, reset to 1
        new_streak_count := 1;
    END IF;

    -- Calculate bonus amount based on streak
    bonus_coins := 50 + (new_streak_count - 1) * 10; -- Base 50 + 10 per streak day
    bonus_coins := LEAST(bonus_coins, 200); -- Cap at 200 coins

    -- Update user
    UPDATE public.users
    SET
        coins = coins + bonus_coins,
        daily_streak = new_streak_count,
        last_daily_bonus = NOW(),
        updated_at = NOW()
    WHERE id = user_uuid;

    -- Return results
    RETURN QUERY SELECT
        current_user.coins + bonus_coins,
        new_streak_count,
        bonus_coins,
        (CURRENT_DATE + INTERVAL '1 day')::TIMESTAMPTZ;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to add purchase rewards
CREATE OR REPLACE FUNCTION public.add_purchase_rewards(
    user_uuid UUID,
    coins_to_add INTEGER DEFAULT 0,
    gems_to_add INTEGER DEFAULT 0,
    spins_to_add INTEGER DEFAULT 0
)
RETURNS TABLE(
    new_coins BIGINT,
    new_gems INTEGER,
    new_spins INTEGER
) AS $$
DECLARE
    current_user RECORD;
BEGIN
    -- Get current user data
    SELECT * INTO current_user FROM public.users WHERE id = user_uuid;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found';
    END IF;

    -- Update user with new rewards
    UPDATE public.users
    SET
        coins = coins + coins_to_add,
        gems = gems + gems_to_add,
        spins = spins + spins_to_add,
        updated_at = NOW()
    WHERE id = user_uuid;

    -- Return new balances
    RETURN QUERY SELECT
        current_user.coins + coins_to_add,
        current_user.gems + gems_to_add,
        current_user.spins + spins_to_add;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to add quest rewards
CREATE OR REPLACE FUNCTION public.add_quest_rewards(
    user_uuid UUID,
    coins_to_add INTEGER DEFAULT 0,
    xp_to_add INTEGER DEFAULT 0,
    gems_to_add INTEGER DEFAULT 0
)
RETURNS TABLE(
    new_coins BIGINT,
    new_xp BIGINT,
    new_level INTEGER,
    new_gems INTEGER
) AS $$
DECLARE
    current_user RECORD;
    new_level_value INTEGER;
BEGIN
    -- Get current user data
    SELECT * INTO current_user FROM public.users WHERE id = user_uuid;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found';
    END IF;

    -- Calculate new level from XP
    new_level_value := public.calculate_level(current_user.xp + xp_to_add);

    -- Update user with quest rewards
    UPDATE public.users
    SET
        coins = coins + coins_to_add,
        xp = xp + xp_to_add,
        level = new_level_value,
        gems = gems + gems_to_add,
        updated_at = NOW()
    WHERE id = user_uuid;

    -- Return new balances
    RETURN QUERY SELECT
        current_user.coins + coins_to_add,
        current_user.xp + xp_to_add,
        new_level_value,
        current_user.gems + gems_to_add;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
